# Development Guide
## Ultimate Electrical Designer Project

### Document Information
- **Purpose**: Specialized guide for Agentic AI sessions on this project
- **Version**: 1.0
- **Date**: July 2025
- **Architecture**: 5-Layer Backend Architecture
- **Methodology**: 5-Phase Implementation Methodology

---

## 1. Project Context & Overview

### 1.1 Application Purpose
The Ultimate Electrical Designer is a professional electrical engineering application built for licensed electrical engineers to design, calculate, and manage electrical systems with IEEE, IEC, and EN standards compliance.

### 1.2 Current Project Status
- **Backend**: 373/373 tests passing (100% pass rate)
- **Frontend**: 66/66 tests passing (100% pass rate)
- **Authentication**: Complete JWT-based system
- **Component Management**: Full CRUD operations implemented
- **Type Safety**: 97.6% MyPy coverage, strict TypeScript mode

### 1.3 Engineering Standards
- **Zero-Tolerance Policy**: No warnings, no technical debt, no incomplete implementations
- **Professional Standards**: IEEE, IEC, EN electrical engineering standards only
- **Quality Metrics**: 90%+ test coverage, 99.9% linting compliance
- **Security**: Zero critical vulnerabilities, comprehensive security validation

---

## 2. Architecture & Technical Stack

### 2.1 Backend Architecture (5-Lay<PERSON> Pattern)

```
server/src/
├── api/                    # Layer 1: API Routes (FastAPI endpoints)
│   ├── v1/                # Versioned API routes
│   │   ├── auth_routes.py # Authentication endpoints
│   │   ├── component_routes.py # Component management
│   │   └── router.py      # Main API router
│   └── main_router.py     # Application router
├── core/                   # Layer 2-4: Business Logic
│   ├── models/            # Layer 2: Data Models (SQLAlchemy)
│   │   ├── auth/          # Authentication models
│   │   ├── general/       # General domain models
│   │   └── base.py        # Base model classes
│   ├── schemas/           # Layer 3: Validation Schemas (Pydantic)
│   │   ├── auth/          # Authentication schemas
│   │   ├── general/       # General domain schemas
│   │   └── base.py        # Base schema classes
│   ├── services/          # Layer 4: Business Logic Services
│   │   ├── auth/          # Authentication services
│   │   ├── general/       # General domain services
│   │   └── dependencies.py # Service dependencies
│   ├── repositories/      # Layer 4: Data Access Layer
│   │   ├── auth/          # Authentication repositories
│   │   ├── general/       # General domain repositories
│   │   └── base_repository.py # Base repository class
│   ├── enums/             # Domain-specific enumerations
│   ├── errors/            # Unified error handling system
│   ├── security/          # Security validation framework
│   └── utils/             # Utility functions and helpers
├── config/                # Layer 5: Configuration
│   └── settings.py        # Application configuration
├── middleware/            # Middleware components
└── main.py               # Application entry point & CLI
```

### 2.2 Frontend Architecture (Component-Based)

```
client/src/
├── app/                   # Next.js App Router
│   ├── (auth)/           # Authentication pages
│   ├── admin/            # Admin dashboard
│   ├── globals.css       # Global styles
│   ├── layout.tsx        # Root layout
│   └── page.tsx          # Home page
├── components/           # React Components
│   ├── auth/            # Authentication components
│   ├── admin/           # Admin components
│   ├── ui/              # shadcn/ui base components
│   └── common/          # Common reusable components
├── hooks/               # Custom React Hooks
│   ├── api/            # API-specific hooks
│   └── useAuth.ts      # Main authentication hook
├── lib/                # Core Libraries
│   ├── api/            # API client (React Query)
│   ├── auth/           # Authentication utilities
│   └── react-query.tsx # React Query configuration
├── stores/             # Zustand State Management
├── types/              # TypeScript Definitions
└── utils/              # Utility Functions
```

### 2.3 Technology Stack

#### Backend Stack
- **Framework**: FastAPI 0.115+ with Uvicorn ASGI server
- **Database**: SQLAlchemy 2.0+ with PostgreSQL (prod) / SQLite (dev)
- **Authentication**: JWT with python-jose, argon2-cffi for password hashing
- **Validation**: Pydantic 2.0+ for request/response validation
- **Testing**: Pytest with 100% real database testing (no mocks)
- **Type Safety**: MyPy with strict type checking
- **Linting**: Ruff for code formatting and linting
- **Scientific**: NumPy, SciPy, Pandas for electrical calculations

#### Frontend Stack
- **Framework**: Next.js 15.3+ with App Router and TypeScript 5.x+
- **Styling**: Tailwind CSS 4.1+ with CSS-in-JS support
- **UI Components**: shadcn/ui (Radix UI primitives + Tailwind)
- **State Management**: 
  - **Server State**: React Query (TanStack Query) 5.x+
  - **Client State**: Zustand 4.x+
- **Forms**: React Hook Form with Zod validation
- **Testing**: Vitest + React Testing Library + Playwright E2E
- **Type Safety**: Strict TypeScript with comprehensive type definitions

---

## 3. Development Patterns & Best Practices

### 3.1 Unified Error Handling Pattern

```python
# Backend: Always use the unified error handling decorator
from core.errors.unified_error_handler import handle_database_errors

@handle_database_errors
async def create_component(component_data: ComponentCreate) -> Component:
    # Implementation with automatic error handling
    pass
```

```typescript
// Frontend: Consistent error handling with React Query
const { data, error, isLoading } = useQuery({
  queryKey: ['components'],
  queryFn: () => apiClient.get('/components'),
  retry: 3,
  retryDelay: 1000
});
```

### 3.2 CRUD Endpoint Factory Pattern

```python
# Backend: Use the CRUD endpoint factory for consistent API endpoints
from core.utils.crud_endpoint_factory import create_crud_endpoints

# Creates standard CRUD endpoints automatically
create_crud_endpoints(
    router=router,
    model=Component,
    schema_create=ComponentCreate,
    schema_update=ComponentUpdate,
    schema_response=ComponentResponse,
    service=component_service
)
```

### 3.3 Performance Monitoring Pattern

```python
# Backend: Apply performance monitoring to critical operations
from core.utils.performance_monitor import monitor_performance

@monitor_performance
async def complex_calculation(data: CalculationInput) -> CalculationResult:
    # Implementation with automatic performance monitoring
    pass
```

### 3.4 Security Validation Pattern

```python
# Backend: Comprehensive security validation
from core.security.input_validators import validate_input

@validate_input
async def process_user_input(input_data: UserInput) -> ProcessedData:
    # Implementation with automatic security validation
    pass
```

### 3.5 Type Safety Patterns

```python
# Backend: Complete type annotations required
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

class ComponentService:
    def __init__(self, repository: ComponentRepository) -> None:
        self.repository = repository
    
    async def get_components(self, filters: Optional[Dict[str, Any]] = None) -> List[Component]:
        return await self.repository.get_all(filters=filters)
```

```typescript
// Frontend: Strict TypeScript with comprehensive interfaces
interface ComponentData {
  id: string;
  name: string;
  category: ComponentCategory;
  specifications: ComponentSpecifications;
  createdAt: Date;
}

const ComponentList: React.FC<{ components: ComponentData[] }> = ({ components }) => {
  // Implementation with full type safety
};
```

---

## 4. Database & Data Management

### 4.1 Database Architecture
- **Development**: SQLite database in `server/data/app_dev.db`
- **Production**: PostgreSQL with connection pooling
- **Migrations**: Alembic for database version control
- **Testing**: Real database connections (no mocks policy)

### 4.2 Model Pattern
```python
# Base model with common fields
class BaseModel(SQLAlchemyBase):
    id: Mapped[str] = mapped_column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    class Config:
        arbitrary_types_allowed = True
```

### 4.3 Repository Pattern
```python
# Generic repository with type safety
class BaseRepository(Generic[T]):
    def __init__(self, model: Type[T], session: AsyncSession) -> None:
        self.model = model
        self.session = session
    
    async def get_by_id(self, id: str) -> Optional[T]:
        return await self.session.get(self.model, id)
    
    async def get_all(self, filters: Optional[Dict[str, Any]] = None) -> List[T]:
        # Implementation with filtering support
        pass
```

### 4.4 Database Commands
```bash
# Common database operations
cd server/

# Create migration
poetry run alembic revision --autogenerate -m "description"

# Apply migrations
poetry run python main.py migrate

# Reset database (development only)
poetry run python main.py wipe-database --confirm

# Create admin user
poetry run python main.py create-superuser 'Admin' 'Pass123' '<EMAIL>'
```

---

## 5. Authentication & Security

### 5.1 Authentication Flow
1. User authenticates via `/api/v1/auth/login`
2. JWT token generated with user claims
3. Token stored in localStorage via TokenManager
4. ApiClient attaches token to all requests
5. useAuth hook manages authentication state
6. RouteGuard protects authenticated routes

### 5.2 Security Implementation
```python
# Backend: JWT token generation
from core.security.jwt_handler import create_access_token

async def authenticate_user(credentials: UserCredentials) -> AuthResponse:
    user = await user_service.authenticate(credentials)
    if user:
        token = create_access_token(data={"sub": user.id})
        return AuthResponse(access_token=token, user=user)
    raise HTTPException(401, "Invalid credentials")
```

```typescript
// Frontend: Authentication hook
const useAuth = () => {
  const { data: user, isLoading } = useQuery({
    queryKey: ['auth', 'user'],
    queryFn: () => apiClient.get('/auth/me'),
    retry: false,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  const login = useMutation({
    mutationFn: (credentials: LoginCredentials) => 
      apiClient.post('/auth/login', credentials),
    onSuccess: (response) => {
      TokenManager.setToken(response.data.access_token);
      queryClient.invalidateQueries({ queryKey: ['auth'] });
    }
  });

  return { user, isLoading, login };
};
```

### 5.3 Role-Based Access Control
```python
# Backend: RBAC implementation
from enum import Enum

class UserRole(str, Enum):
    ADMIN = "admin"
    ENGINEER = "engineer"
    SENIOR_ENGINEER = "senior_engineer"

@require_role(UserRole.ADMIN)
async def admin_only_endpoint():
    pass
```

---

## 6. Testing Framework & Quality Assurance

### 6.1 Testing Strategy
- **No Mocks Policy**: Real database connections for integration tests
- **5-Phase Testing**: Unit → Integration → API → E2E → Performance
- **Coverage Requirements**: 90%+ for critical modules, 85%+ for high priority
- **Test Categories**: unit, integration, api, database, security, performance

### 6.2 Backend Testing Patterns
```python
# Pytest with real database
@pytest.mark.asyncio
@pytest.mark.integration
async def test_component_creation(async_session: AsyncSession):
    # Arrange
    component_data = ComponentCreate(
        name="Test Component",
        category_id="test-category"
    )
    
    # Act
    component = await component_service.create(component_data)
    
    # Assert
    assert component.name == "Test Component"
    assert component.id is not None
```

### 6.3 Frontend Testing Patterns
```typescript
// Vitest with React Testing Library
describe('ComponentList', () => {
  it('should render components correctly', async () => {
    const mockComponents = [
      { id: '1', name: 'Component 1', category: 'electrical' },
      { id: '2', name: 'Component 2', category: 'mechanical' }
    ];

    render(
      <QueryClient>
        <ComponentList components={mockComponents} />
      </QueryClient>
    );

    expect(screen.getByText('Component 1')).toBeInTheDocument();
    expect(screen.getByText('Component 2')).toBeInTheDocument();
  });
});
```

### 6.4 E2E Testing with Playwright
```typescript
// Authentication flow testing
test.describe('Authentication Flow', () => {
  test('should login successfully', async ({ page }) => {
    await page.goto('/login');
    await page.fill('[data-testid="username"]', 'admin');
    await page.fill('[data-testid="password"]', 'Pass123');
    await page.click('[data-testid="login-button"]');
    
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
  });
});
```

---

## 7. Development Workflow & Commands

### 7.1 Backend Development Commands
```bash
# From server/ directory
poetry install                              # Install dependencies
poetry run python main.py run --reload     # Development server
poetry run pytest                          # Run all tests
poetry run pytest -v -m unit              # Unit tests only
poetry run pytest -v -m integration       # Integration tests only
poetry run pytest --cov=src --cov-report=html # Coverage report
poetry run mypy src/                       # Type checking
poetry run ruff check .                    # Linting
poetry run ruff format .                   # Code formatting
```

### 7.2 Frontend Development Commands
```bash
# From client/ directory
npm install                                # Install dependencies
npm run dev                               # Development server
npm run build                             # Production build
npm run type-check                        # TypeScript checking
npm run lint                              # ESLint
npm run test                              # Unit tests
npm run test:coverage                     # Coverage report
npm run test:e2e                          # E2E tests
```

### 7.3 Global Development Commands
```bash
# From root directory
make help                                 # Show all commands
make install                              # Install all dependencies
make type-check                           # Full type checking
make test                                 # Run all tests
make lint                                 # Run linting
make clean                                # Clean temporary files
make dev                                  # Start backend server
```

---

## 8. Frontend Development Guide (shadcn/ui)

### 8.1 Component Architecture & Design System
The project uses **shadcn/ui** as the primary UI component library, built on:
- **Radix UI**: Headless, accessible component primitives
- **Tailwind CSS**: Utility-first CSS framework
- **Atomic Design**: Components organized as atoms, molecules, organisms
- **Professional Theme**: Electrical engineering specific design tokens

### 8.2 Professional UI Component Patterns

#### 8.2.1 Electrical Engineering Components
```typescript
// Professional electrical engineering UI components
import { Button } from 'components/ui/button';
import { Input } from 'components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from 'components/ui/card';
import { Badge } from 'components/ui/badge';
import { AlertTriangle, Calculator, Download, CheckCircle } from 'lucide-react';

// Electrical Input Component (Atom)
export const ElectricalInput: React.FC<ElectricalInputProps> = ({
  label,
  value,
  onChange,
  unit,
  min,
  max,
  precision = 2,
  validation
}) => {
  const [error, setError] = useState<string | null>(null);
  
  const handleChange = (newValue: number) => {
    // Validate electrical engineering constraints
    if (validation) {
      const validationResult = validation(newValue);
      setError(validationResult.error);
    }
    onChange(newValue);
  };
  
  return (
    <div className="flex flex-col space-y-2">
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <div className="flex items-center space-x-2">
        <Input
          type="number"
          value={value}
          onChange={(e) => handleChange(parseFloat(e.target.value))}
          min={min}
          max={max}
          step={Math.pow(10, -precision)}
          className={`flex-1 ${error ? 'border-red-500' : 'border-gray-300'}`}
        />
        <span className="text-sm text-gray-500 min-w-[3rem]">{unit}</span>
      </div>
      {error && (
        <div className="flex items-center text-red-600 text-xs">
          <AlertTriangle className="w-3 h-3 mr-1" />
          {error}
        </div>
      )}
    </div>
  );
};

// Electrical Meter Component (Molecule)
export const ElectricalMeter: React.FC<ElectricalMeterProps> = ({
  label,
  value,
  unit,
  min,
  max,
  precision = 2,
  color = 'primary',
  showAlert = true,
  standards = []
}) => {
  const percentage = ((value - min) / (max - min)) * 100;
  const isAlert = showAlert && (percentage > 90 || percentage < 10);
  
  return (
    <Card className={`p-4 ${isAlert ? 'border-red-500' : 'border-gray-200'}`}>
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700">{label}</span>
        <span className={`text-lg font-bold ${
          isAlert ? 'text-red-600' : 'text-gray-900'
        }`}>
          {value.toFixed(precision)} {unit}
        </span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${
            isAlert ? 'bg-red-500' : `bg-${color}-500`
          }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
      
      <div className="flex justify-between text-xs text-gray-500 mt-1">
        <span>{min}</span>
        <span>{max}</span>
      </div>
      
      {standards.length > 0 && (
        <div className="flex gap-1 mt-2">
          {standards.map((standard) => (
            <Badge key={standard} variant="secondary" className="text-xs">
              {standard}
            </Badge>
          ))}
        </div>
      )}
      
      {isAlert && (
        <div className="mt-2 flex items-center text-red-600">
          <AlertTriangle className="w-4 h-4 mr-1" />
          <span className="text-xs">Value outside normal range</span>
        </div>
      )}
    </Card>
  );
};

// Calculation Results Component (Organism)
export const CalculationResults: React.FC<CalculationResultsProps> = ({
  results,
  standards,
  onExport,
  onValidate
}) => {
  return (
    <Card className="p-6">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Calculator className="w-5 h-5 mr-2" />
          Calculation Results
        </CardTitle>
        <div className="flex gap-2">
          {standards.map((standard) => (
            <Badge key={standard} variant="secondary">
              {standard}
            </Badge>
          ))}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <ElectricalMeter
            label="Load Current"
            value={results.current_load}
            unit="A"
            min={0}
            max={results.max_current}
            color="blue"
            standards={['IEEE-141']}
          />
          
          <ElectricalMeter
            label="Power Consumption"
            value={results.power_consumption}
            unit="W"
            min={0}
            max={results.max_power}
            color="purple"
            standards={['IEEE-141']}
          />
          
          <ElectricalMeter
            label="Voltage Drop"
            value={results.voltage_drop}
            unit="V"
            min={0}
            max={results.max_voltage_drop}
            color="orange"
            standards={['IEEE-141', 'IEC-60364']}
          />
        </div>
        
        <div className="mt-6 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span className="text-sm text-green-600">
              IEEE-141 Compliant
            </span>
          </div>
          
          <div className="flex gap-2">
            <Button onClick={onValidate} variant="outline">
              Validate Standards
            </Button>
            <Button onClick={onExport} variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
```

#### 8.2.2 Professional Form Components
```typescript
// Professional Component Specification Form
export const ComponentSpecificationForm: React.FC<ComponentSpecificationFormProps> = ({
  specifications,
  onChange,
  onSubmit,
  isLoading = false
}) => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const validateVoltage = (value: number): ValidationResult => {
    if (value <= 0) return { error: 'Voltage must be positive' };
    if (value > 1000000) return { error: 'Voltage exceeds maximum limit (1MV)' };
    return { error: null };
  };
  
  const validateCurrent = (value: number): ValidationResult => {
    if (value <= 0) return { error: 'Current must be positive' };
    if (value > 100000) return { error: 'Current exceeds maximum limit (100kA)' };
    return { error: null };
  };
  
  const validatePowerFactor = (value: number): ValidationResult => {
    if (value < 0 || value > 1) return { error: 'Power factor must be between 0 and 1' };
    return { error: null };
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Electrical Specifications</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={onSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ElectricalInput
              label="Voltage Rating"
              value={specifications.voltage_rating}
              onChange={(value) => onChange({ ...specifications, voltage_rating: value })}
              unit="V"
              min={0}
              max={1000000}
              precision={1}
              validation={validateVoltage}
            />
            
            <ElectricalInput
              label="Current Rating"
              value={specifications.current_rating}
              onChange={(value) => onChange({ ...specifications, current_rating: value })}
              unit="A"
              min={0}
              max={100000}
              precision={2}
              validation={validateCurrent}
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ElectricalInput
              label="Power Rating"
              value={specifications.power_rating}
              onChange={(value) => onChange({ ...specifications, power_rating: value })}
              unit="W"
              min={0}
              max={10000000}
              precision={0}
            />
            
            <ElectricalInput
              label="Power Factor"
              value={specifications.power_factor}
              onChange={(value) => onChange({ ...specifications, power_factor: value })}
              unit=""
              min={0}
              max={1}
              precision={3}
              validation={validatePowerFactor}
            />
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={() => onChange({})}>
              Reset
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : 'Save Specifications'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
```

### 8.3 Advanced State Management Patterns

#### 8.3.1 Professional React Query Integration
```typescript
// Advanced API hooks for electrical engineering
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Component Management with Professional Features
export const useComponents = (filters?: ComponentFilters) => {
  return useQuery({
    queryKey: ['components', filters],
    queryFn: () => componentsApi.getComponents(filters),
    select: (data) => data.data,
    staleTime: 2 * 60 * 1000, // 2 minutes for component data
    onError: (error) => {
      toast.error(`Failed to load components: ${error.message}`);
    }
  });
};

export const useComponentWithCalculations = (id: string) => {
  return useQuery({
    queryKey: ['component-with-calculations', id],
    queryFn: async () => {
      const [component, calculations] = await Promise.all([
        componentsApi.getComponent(id),
        calculationsApi.getComponentCalculations(id)
      ]);
      return { component: component.data, calculations: calculations.data };
    },
    enabled: !!id,
    staleTime: 1 * 60 * 1000, // 1 minute for calculation data
  });
};

// Professional Calculation Hook
export const useElectricalCalculation = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  return useMutation({
    mutationFn: calculationsApi.calculateLoad,
    onMutate: async (variables) => {
      // Optimistic update
      await queryClient.cancelQueries({ queryKey: ['calculations'] });
      
      const previousCalculations = queryClient.getQueryData(['calculations']);
      
      queryClient.setQueryData(['calculations'], (old: any) => {
        return {
          ...old,
          data: [
            ...old.data,
            {
              id: 'temp-' + Date.now(),
              type: variables.calculation_type,
              status: 'calculating',
              created_at: new Date().toISOString()
            }
          ]
        };
      });
      
      return { previousCalculations };
    },
    onSuccess: (data) => {
      // Cache calculation result
      queryClient.setQueryData(
        ['calculation', data.data.calculation_id], 
        data
      );
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['calculations'] });
      queryClient.invalidateQueries({ queryKey: ['component-calculations'] });
      
      toast.success('Calculation completed successfully');
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      if (context?.previousCalculations) {
        queryClient.setQueryData(['calculations'], context.previousCalculations);
      }
      
      toast.error(`Calculation failed: ${error.message}`);
    }
  });
};

// Professional Standards Validation Hook
export const useStandardsValidation = () => {
  return useMutation({
    mutationFn: standardsApi.validateCompliance,
    onSuccess: (data) => {
      if (data.data.overall_compliance) {
        toast.success('Standards compliance verified');
      } else {
        toast.warning('Standards compliance issues found');
      }
    },
    onError: (error) => {
      toast.error(`Standards validation failed: ${error.message}`);
    }
  });
};
```

#### 8.3.2 Professional Zustand Store Patterns
```typescript
// Professional Calculation Store
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface CalculationState {
  currentCalculation: CalculationSession | null;
  calculationHistory: CalculationResult[];
  activeStandards: string[];
  preferences: CalculationPreferences;
  
  // Actions
  startCalculation: (type: string, parameters: any) => void;
  updateCalculationParameters: (parameters: Partial<any>) => void;
  completeCalculation: (result: CalculationResult) => void;
  setActiveStandards: (standards: string[]) => void;
  updatePreferences: (preferences: Partial<CalculationPreferences>) => void;
  
  // Professional Features
  validateAgainstStandards: (result: CalculationResult) => StandardsValidationResult;
  generateReport: (calculationId: string) => Promise<ReportData>;
  exportCalculation: (calculationId: string, format: string) => Promise<Blob>;
}

export const useCalculationStore = create<CalculationState>()(n  persist(
    (set, get) => ({
      currentCalculation: null,
      calculationHistory: [],
      activeStandards: ['IEEE-141', 'IEC-60364'],
      preferences: {
        units: 'metric',
        precision: 2,
        autoValidate: true,
        showWarnings: true
      },
      
      startCalculation: (type, parameters) => {
        set({
          currentCalculation: {
            id: `calc-${Date.now()}`,
            type,
            parameters,
            status: 'in_progress',
            startedAt: new Date(),
            standards: get().activeStandards
          }
        });
      },
      
      updateCalculationParameters: (parameters) => {
        set(state => ({
          currentCalculation: state.currentCalculation ? {
            ...state.currentCalculation,
            parameters: { ...state.currentCalculation.parameters, ...parameters }
          } : null
        }));
      },
      
      completeCalculation: (result) => {
        set(state => ({
          currentCalculation: null,
          calculationHistory: [result, ...state.calculationHistory.slice(0, 99)] // Keep last 100
        }));
      },
      
      setActiveStandards: (standards) => {
        set({ activeStandards: standards });
      },
      
      updatePreferences: (preferences) => {
        set(state => ({
          preferences: { ...state.preferences, ...preferences }
        }));
      },
      
      validateAgainstStandards: (result) => {
        const { activeStandards } = get();
        return standardsValidator.validate(result, activeStandards);
      },
      
      generateReport: async (calculationId) => {
        const calculation = get().calculationHistory.find(c => c.id === calculationId);
        if (!calculation) throw new Error('Calculation not found');
        
        return reportGenerator.generateProfessionalReport(calculation);
      },
      
      exportCalculation: async (calculationId, format) => {
        const calculation = get().calculationHistory.find(c => c.id === calculationId);
        if (!calculation) throw new Error('Calculation not found');
        
        return exportService.exportCalculation(calculation, format);
      }
    }),
    {
      name: 'calculation-store',
      partialize: (state) => ({
        calculationHistory: state.calculationHistory,
        activeStandards: state.activeStandards,
        preferences: state.preferences
      })
    }
  )
);
```

### 8.4 Professional Theme and Styling

#### 8.4.1 Electrical Engineering Design Tokens
```typescript
// Professional Tailwind CSS configuration
const config = {
  theme: {
    extend: {
      colors: {
        // Primary Brand Colors
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',  // Engineering blue
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        
        // Professional Status Colors
        danger: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',  // Alert red
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',  // Success green
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',  // Warning amber
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        
        // Electrical Engineering Specific Colors
        electrical: {
          voltage: '#ff6b35',    // Voltage orange
          current: '#4a90e2',    // Current blue
          power: '#7b68ee',      // Power purple
          ground: '#8b4513',     // Ground brown
          neutral: '#a8a8a8',    // Neutral gray
          protection: '#ff1493', // Protection magenta
        },
        
        // Professional Grays
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',  // Professional gray
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        }
      },
      
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace'],
        engineering: ['Roboto Mono', 'monospace'], // Technical drawings
      },
      
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
      },
      
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
        '144': '36rem',
      },
      
      borderRadius: {
        'xs': '0.125rem',
        'sm': '0.25rem',
        'md': '0.375rem',
        'lg': '0.5rem',
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      
      boxShadow: {
        'professional': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'engineering': '0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 10px -3px rgba(0, 0, 0, 0.05)',
        'calculation': '0 10px 40px -10px rgba(59, 130, 246, 0.15)',
      },
      
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'calculation': 'calculation 2s ease-in-out infinite',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        calculation: {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.05)' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
    require('@tailwindcss/container-queries'),
  ],
};
```

#### 8.4.2 Professional Component Styling
```typescript
// Professional CSS-in-JS patterns with Tailwind
export const professionalStyles = {
  // Card styles for different contexts
  card: {
    base: 'bg-white rounded-lg shadow-professional border border-gray-200',
    calculation: 'bg-white rounded-lg shadow-calculation border border-blue-200',
    warning: 'bg-yellow-50 rounded-lg shadow-professional border border-yellow-200',
    error: 'bg-red-50 rounded-lg shadow-professional border border-red-200',
    success: 'bg-green-50 rounded-lg shadow-professional border border-green-200',
  },
  
  // Button styles for professional actions
  button: {
    primary: 'bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors',
    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors',
    calculate: 'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors',
    export: 'bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors',
    danger: 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors',
  },
  
  // Input styles for electrical engineering
  input: {
    base: 'border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500',
    electrical: 'border border-blue-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono',
    error: 'border border-red-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500',
    success: 'border border-green-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500',
  },
  
  // Professional typography
  typography: {
    heading: 'text-gray-900 font-semibold',
    subheading: 'text-gray-700 font-medium',
    body: 'text-gray-600',
    caption: 'text-gray-500 text-sm',
    technical: 'font-mono text-gray-800',
    electrical: 'font-mono text-blue-800',
  },
  
  // Status indicators
  status: {
    compliant: 'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium',
    warning: 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium',
    error: 'bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium',
    calculating: 'bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium animate-pulse',
  }
};
```

### 8.5 Advanced Frontend Patterns

#### 8.5.1 Professional Error Handling
```typescript
// Professional Error Boundary
export class ElectricalErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log error to professional monitoring system
    errorLogger.logError(error, {
      component: 'ElectricalErrorBoundary',
      errorInfo,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    });
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <Card className="p-6 border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Calculation Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700 mb-4">
              An error occurred during the electrical calculation. Please check your input parameters and try again.
            </p>
            <Button 
              onClick={() => this.setState({ hasError: false, error: null })}
              variant="outline"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      );
    }
    
    return this.props.children;
  }
}

// Professional Error Toast System
export const useElectricalToast = () => {
  const showCalculationError = (error: CalculationError) => {
    toast.error(
      <div className="flex items-center space-x-2">
        <AlertTriangle className="w-4 h-4 text-red-500" />
        <div>
          <div className="font-medium">Calculation Failed</div>
          <div className="text-sm text-gray-600">{error.message}</div>
          {error.recommendations && (
            <div className="text-xs text-gray-500 mt-1">
              Recommendation: {error.recommendations}
            </div>
          )}
        </div>
      </div>,
      {
        duration: 8000,
        position: 'top-right',
      }
    );
  };
  
  const showStandardsWarning = (warning: StandardsWarning) => {
    toast.warning(
      <div className="flex items-center space-x-2">
        <AlertTriangle className="w-4 h-4 text-yellow-500" />
        <div>
          <div className="font-medium">Standards Compliance Warning</div>
          <div className="text-sm text-gray-600">{warning.message}</div>
          <div className="text-xs text-gray-500 mt-1">
            Standard: {warning.standard}
          </div>
        </div>
      </div>,
      {
        duration: 10000,
        position: 'top-right',
      }
    );
  };
  
  return { showCalculationError, showStandardsWarning };
};
```

#### 8.5.2 Professional Loading States
```typescript
// Professional Loading Components
export const CalculationLoader: React.FC<{ message?: string }> = ({ 
  message = 'Performing electrical calculations...' 
}) => {
  return (
    <div className="flex flex-col items-center justify-center p-8">
      <div className="relative">
        <div className="w-16 h-16 border-4 border-blue-200 rounded-full animate-spin border-t-blue-600"></div>
        <Calculator className="w-8 h-8 text-blue-600 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
      </div>
      <p className="mt-4 text-gray-600 text-center">{message}</p>
      <p className="mt-2 text-sm text-gray-500">Please wait while we process your calculation...</p>
    </div>
  );
};

export const ComponentLoader: React.FC = () => {
  return (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <Card key={i} className="p-4">
          <div className="animate-pulse">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};
```

#### 8.5.3 Professional Accessibility
```typescript
// Professional Accessibility Hooks
export const useElectricalKeyboardShortcuts = () => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+Enter: Quick calculate
      if (event.ctrlKey && event.key === 'Enter') {
        event.preventDefault();
        document.getElementById('calculate-button')?.click();
      }
      
      // Ctrl+S: Save calculation
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault();
        document.getElementById('save-button')?.click();
      }
      
      // Ctrl+E: Export results
      if (event.ctrlKey && event.key === 'e') {
        event.preventDefault();
        document.getElementById('export-button')?.click();
      }
      
      // Esc: Cancel current operation
      if (event.key === 'Escape') {
        document.getElementById('cancel-button')?.click();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);
};

// Professional Focus Management
export const useFocusManagement = () => {
  const focusFirstInput = () => {
    const firstInput = document.querySelector('input[type="number"]') as HTMLInputElement;
    firstInput?.focus();
  };
  
  const focusNextInput = (currentInput: HTMLInputElement) => {
    const inputs = Array.from(document.querySelectorAll('input[type="number"]'));
    const currentIndex = inputs.indexOf(currentInput);
    const nextInput = inputs[currentIndex + 1] as HTMLInputElement;
    nextInput?.focus();
  };
  
  const focusPreviousInput = (currentInput: HTMLInputElement) => {
    const inputs = Array.from(document.querySelectorAll('input[type="number"]'));
    const currentIndex = inputs.indexOf(currentInput);
    const previousInput = inputs[currentIndex - 1] as HTMLInputElement;
    previousInput?.focus();
  };
  
  return { focusFirstInput, focusNextInput, focusPreviousInput };
};
```

---

## 9. Frontend Performance & Optimization

### 9.1 Performance Optimization Strategies

#### 9.1.1 React Query Optimization
```typescript
// Professional React Query Performance
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Electrical engineering specific caching
      staleTime: 5 * 60 * 1000, // 5 minutes for component data
      cacheTime: 10 * 60 * 1000, // 10 minutes cache retention
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Background refetch for critical data
      refetchOnWindowFocus: (query) => {
        return query.queryKey[0] === 'calculations' || query.queryKey[0] === 'standards';
      },
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
      // Optimistic updates for better UX
      onMutate: async (variables) => {
        // Professional optimistic update patterns
        await queryClient.cancelQueries({ queryKey: ['components'] });
        const previousData = queryClient.getQueryData(['components']);
        return { previousData };
      },
    },
  },
});

// Professional prefetching strategies
export const useElectricalPrefetch = () => {
  const queryClient = useQueryClient();
  
  const prefetchRelatedComponents = async (categoryId: string) => {
    await queryClient.prefetchQuery({
      queryKey: ['components', { category: categoryId }],
      queryFn: () => componentsApi.getComponentsByCategory(categoryId),
      staleTime: 2 * 60 * 1000,
    });
  };
  
  const prefetchCalculationStandards = async (calculationType: string) => {
    await queryClient.prefetchQuery({
      queryKey: ['standards', calculationType],
      queryFn: () => standardsApi.getStandardsForCalculationType(calculationType),
      staleTime: 10 * 60 * 1000, // Standards change infrequently
    });
  };
  
  return { prefetchRelatedComponents, prefetchCalculationStandards };
};
```

#### 9.1.2 Code Splitting & Bundle Optimization
```typescript
// Professional code splitting patterns
import dynamic from 'next/dynamic';

// Lazy load heavy calculation components
const ElectricalCalculator = dynamic(
  () => import('components/calculations/ElectricalCalculator'),
  {
    loading: () => <CalculationLoader message="Loading calculation engine..." />,
    ssr: false // Calculation components don't need SSR
  }
);

const ComponentLibrary = dynamic(
  () => import('components/components/ComponentLibrary'),
  {
    loading: () => <ComponentLoader />,
    ssr: true // Component library benefits from SSR
  }
);

const ReportGenerator = dynamic(
  () => import('components/reports/ReportGenerator'),
  {
    loading: () => <div>Loading report generator...</div>,
    ssr: false // Reports are client-side only
  }
);

// Professional route-based code splitting
export default function CalculationPage() {
  return (
    <Suspense fallback={<CalculationLoader />}>
      <ElectricalCalculator />
    </Suspense>
  );
}
```

#### 9.1.3 Memory Management & Cleanup
```typescript
// Professional memory management
export const useElectricalMemoryManagement = () => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const calculationRef = useRef<CalculationWorker | null>(null);
  
  useEffect(() => {
    // Cleanup on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (calculationRef.current) {
        calculationRef.current.terminate();
      }
    };
  }, []);
  
  const startCalculationWorker = useCallback(() => {
    if (calculationRef.current) {
      calculationRef.current.terminate();
    }
    
    calculationRef.current = new Worker(
      new URL('@/workers/electrical-calculation.worker.ts', import.meta.url)
    );
    
    return calculationRef.current;
  }, []);
  
  return { startCalculationWorker };
};

// Professional cleanup patterns
export const useElectricalCleanup = () => {
  const queryClient = useQueryClient();
  
  const cleanupStaleCalculations = useCallback(() => {
    // Remove calculations older than 1 hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    queryClient.removeQueries({
      queryKey: ['calculations'],
      predicate: (query) => {
        const data = query.state.data as any;
        return data?.timestamp && new Date(data.timestamp) < oneHourAgo;
      }
    });
  }, [queryClient]);
  
  const cleanupComponentCache = useCallback(() => {
    // Remove unused component data
    queryClient.removeQueries({
      queryKey: ['components'],
      predicate: (query) => {
        return query.getObserversCount() === 0;
      }
    });
  }, [queryClient]);
  
  return { cleanupStaleCalculations, cleanupComponentCache };
};
```

### 9.2 Professional Monitoring & Analytics

#### 9.2.1 Frontend Performance Monitoring
```typescript
// Professional performance monitoring
export const usePerformanceMonitoring = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  
  const measureCalculationPerformance = useCallback(
    async (calculationFn: () => Promise<any>) => {
      const startTime = performance.now();
      const startMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      try {
        const result = await calculationFn();
        
        const endTime = performance.now();
        const endMemory = (performance as any).memory?.usedJSHeapSize || 0;
        
        const executionTime = endTime - startTime;
        const memoryUsage = endMemory - startMemory;
        
        setMetrics(prev => ({
          ...prev,
          lastCalculation: {
            executionTime,
            memoryUsage,
            timestamp: new Date().toISOString()
          }
        }));
        
        // Log performance metrics
        if (executionTime > 2000) { // > 2 seconds
          console.warn('Slow calculation detected:', {
            executionTime,
            memoryUsage,
            function: calculationFn.name
          });
        }
        
        return result;
      } catch (error) {
        console.error('Calculation performance measurement failed:', error);
        throw error;
      }
    },
    []
  );
  
  const measureComponentRender = useCallback((componentName: string) => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      setMetrics(prev => ({
        ...prev,
        componentRenders: {
          ...prev.componentRenders,
          [componentName]: renderTime
        }
      }));
      
      if (renderTime > 100) { // > 100ms
        console.warn('Slow component render:', {
          componentName,
          renderTime
        });
      }
    };
  }, []);
  
  return { metrics, measureCalculationPerformance, measureComponentRender };
};
```

#### 9.2.2 User Analytics & Insights
```typescript
// Professional user analytics
export const useElectricalAnalytics = () => {
  const trackCalculationUsage = useCallback((calculationType: string, parameters: any) => {
    // Professional analytics tracking
    analytics.track('calculation_performed', {
      type: calculationType,
      parameters: sanitizeParameters(parameters),
      timestamp: new Date().toISOString(),
      sessionId: getSessionId()
    });
  }, []);
  
  const trackComponentInteraction = useCallback((action: string, componentId: string) => {
    analytics.track('component_interaction', {
      action,
      componentId,
      timestamp: new Date().toISOString()
    });
  }, []);
  
  const trackStandardsUsage = useCallback((standards: string[]) => {
    analytics.track('standards_usage', {
      standards,
      timestamp: new Date().toISOString()
    });
  }, []);
  
  return { trackCalculationUsage, trackComponentInteraction, trackStandardsUsage };
};
```

### 9.3 Backend Performance Integration

#### 9.3.1 API Performance Monitoring
```typescript
// Frontend API performance monitoring
export const apiPerformanceInterceptor = {
  request: (config: any) => {
    config.metadata = { startTime: new Date() };
    return config;
  },
  
  response: (response: any) => {
    const endTime = new Date();
    const duration = endTime.getTime() - response.config.metadata.startTime.getTime();
    
    // Log slow API calls
    if (duration > 2000) {
      console.warn('Slow API call detected:', {
        url: response.config.url,
        method: response.config.method,
        duration,
        status: response.status
      });
    }
    
    // Track API performance metrics
    apiMetrics.track({
      endpoint: response.config.url,
      method: response.config.method,
      duration,
      status: response.status,
      timestamp: new Date().toISOString()
    });
    
    return response;
  },
  
  error: (error: any) => {
    const endTime = new Date();
    const duration = endTime.getTime() - error.config.metadata.startTime.getTime();
    
    // Log API errors with performance context
    console.error('API error with performance context:', {
      url: error.config.url,
      method: error.config.method,
      duration,
      status: error.response?.status,
      message: error.message
    });
    
    return Promise.reject(error);
  }
};
```

---

## 10. Deployment & DevOps

### 10.1 Environment Configuration
- **Development**: SQLite database, local file storage
- **Production**: PostgreSQL database, cloud storage
- **Environment Variables**: Comprehensive environment configuration
- **Docker**: Containerized deployment with Docker Compose

### 10.2 CI/CD Pipeline
- **Pre-commit Hooks**: Husky + lint-staged for code quality
- **Automated Testing**: Full test suite on every commit
- **Type Checking**: MyPy and TypeScript validation
- **Security Scanning**: Automated vulnerability assessments

### 10.3 Monitoring & Logging
- **Application Monitoring**: Comprehensive logging framework
- **Error Tracking**: Structured error reporting
- **Performance Metrics**: Response time and throughput monitoring
- **Security Monitoring**: Security event logging and alerting

---

## 11. Electrical Engineering Domain

### 11.1 Standards Compliance
- **IEEE Standards**: IEEE-141 (Power System Analysis), IEEE-142 (Grounding)
- **IEC Standards**: IEC-60079 (Explosive Atmospheres), IEC-61508 (Safety)
- **EN Standards**: EN-50110 (Safety), EN-60204 (Machinery Safety)

### 11.2 Calculation Engine
```python
# Electrical calculations with standards compliance
from core.calculations.electrical import ElectricalCalculator

calculator = ElectricalCalculator()
result = await calculator.calculate_load(
    components=components,
    voltage=480,
    power_factor=0.85,
    standard="IEEE-141"
)
```

### 11.3 Professional Documentation
- **Calculation Reports**: IEEE-compliant calculation documentation
- **Compliance Certificates**: Standards compliance verification
- **Professional Seals**: Digital signature support for PE stamps
- **Audit Trails**: Complete documentation of all calculations

---

## 12. Development Guidelines for Claude

### 12.1 Code Quality Standards
- **Zero-Tolerance Policy**: No warnings, no incomplete implementations
- **Type Safety**: 100% type annotations (Python), strict TypeScript
- **Testing**: Real database testing, comprehensive coverage
- **Documentation**: Complete inline documentation for all public APIs

### 12.2 Implementation Approach
- **5-Phase Methodology**: Always follow the established phases
- **Unified Patterns**: Use established error handling and performance patterns
- **Standards Compliance**: Ensure all implementations meet electrical standards
- **Professional Quality**: Engineering-grade attention to detail

### 12.3 Common Tasks
- **API Development**: Use CRUD endpoint factory for consistency
- **Database Operations**: Follow repository pattern with type safety
- **Frontend Components**: Use shadcn/ui components with proper TypeScript
- **Testing**: Write comprehensive tests with real database connections
- **Documentation**: Update relevant documentation with changes

### 12.4 Troubleshooting
- **Database Issues**: Check migration status and connection configuration
- **Type Errors**: Verify MyPy and TypeScript configurations
- **Test Failures**: Ensure proper test database setup
- **Performance Issues**: Check query optimization and caching

---

## 13. Quick Reference

### 13.1 Project Structure
```
ultimate-electrical-designer/
├── server/                 # Backend (Python FastAPI)
├── client/                 # Frontend (Next.js TypeScript)
├── docs/                   # Project documentation
├── Makefile               # Global development commands
├── PRD.md                 # Product requirements
├── PLANNING.md            # Project planning
├── TASKS.md               # Task management
└── CLAUDE.md              # This guide
```

### 13.2 Key Files
- **Backend Entry**: `server/src/main.py`
- **Frontend Entry**: `client/src/app/page.tsx`
- **API Router**: `server/src/api/main_router.py`
- **Auth Hook**: `client/src/hooks/useAuth.ts`
- **Database Models**: `server/src/core/models/`
- **UI Components**: `client/src/components/ui/`

### 13.3 Development Checklist
- [ ] Run tests: `make test`
- [ ] Check types: `make type-check`
- [ ] Run linting: `make lint`
- [ ] Update documentation
- [ ] Verify standards compliance
- [ ] Test with real database
- [ ] Review security implications
- [ ] Check performance impact

---

This guide provides comprehensive context for Claude Code sessions on the Ultimate Electrical Designer project. Always refer to this guide for project-specific patterns, standards, and best practices to ensure consistency with the established engineering-grade development approach.