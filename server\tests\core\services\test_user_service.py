import pytest
from unittest.mock import MagicMock, patch
from sqlalchemy.orm import Session

from src.core.services.general.user_service import UserService
from src.core.services.general.user_service import UserService
from src.core.schemas.general.user_schemas import LoginRequestSchema, PasswordChangeRequestSchema, UserCreateSchema
from src.core.security.password_handler import Password<PERSON>andler
from src.core.errors.exceptions import InvalidInputError, NotFoundError
from src.core.models.general.user import User
from src.core.enums import UserRole

class TestUserService:
    """Unit tests for the UserService class."""

    def test_create_user_success(self, user_service: UserService):
        """Test successful user creation."""
        user_data = UserCreateSchema(
            name="testuser",
            email="<EMAIL>",
            password="A_Strong_Password_123!",
            role=UserRole.ELECTRICAL_DESIGNER,
            is_active=True
        )
        mock_user = User(**{
            "id": 1,
            "name": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hashed_password",
            "role": UserRole.ELECTRICAL_DESIGNER,
            "is_active": True,
            "is_admin": False,
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00"
        })

        with patch.object(user_service.user_repo, 'get_by_email', return_value=None), \
             patch.object(user_service.user_repo, 'create', return_value=mock_user):
            
            created_user = user_service.create_user(user_data)
            
            assert created_user.name == user_data.name
            assert created_user.email == user_data.email
            user_service.user_repo.create.assert_called_once()

    def test_authenticate_user_success(self, user_service: UserService):
        """Test successful user authentication."""
        password = "A_Strong_Password_123!"
        hashed_password = PasswordHandler.hash_password(password)
        user = User(**{
            "id": 1,
            "name": "testuser",
            "email": "<EMAIL>",
            "password_hash": hashed_password,
            "is_active": True,
            "role": UserRole.ELECTRICAL_DESIGNER,
            "is_admin": False,
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00"
        })
        login_data = LoginRequestSchema(username="<EMAIL>", password=password)

        with patch.object(user_service.user_repo, 'get_by_email', return_value=user), \
             patch('src.core.security.password_handler.PasswordHandler.verify_password', return_value=True), \
             patch('src.core.security.password_handler.PasswordHandler.needs_rehash', return_value=False):
            
            authenticated_user = user_service.authenticate_user(login_data)
            assert authenticated_user.email == user.email

    def test_authenticate_user_failure_wrong_password(self, user_service: UserService):
        """Test user authentication failure with a wrong password."""
        password = "A_Strong_Password_123!"
        hashed_password = PasswordHandler.hash_password(password)
        user = User(**{
            "id": 1,
            "name": "testuser",
            "email": "<EMAIL>",
            "password_hash": hashed_password,
            "is_active": True,
            "role": UserRole.ELECTRICAL_DESIGNER,
            "is_admin": False,
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00"
        })
        login_data = LoginRequestSchema(username="<EMAIL>", password="wrong_password")

        with patch.object(user_service.user_repo, 'get_by_email', return_value=user), \
             patch('src.core.security.password_handler.PasswordHandler.verify_password', return_value=False):
            with pytest.raises(InvalidInputError, match="Invalid email or password"):
                user_service.authenticate_user(login_data)

    def test_authenticate_user_needs_rehash(self, user_service: UserService):
        """Test that the password is rehashed if needed upon login."""
        password = "A_Strong_Password_123!"
        old_hash = PasswordHandler.hash_password(password)
        user = User(**{
            "id": 1,
            "name": "testuser",
            "email": "<EMAIL>",
            "password_hash": old_hash,
            "is_active": True,
            "role": UserRole.ELECTRICAL_DESIGNER,
            "is_admin": False,
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00"
        })
        login_data = LoginRequestSchema(username="<EMAIL>", password=password)

        with patch.object(user_service.user_repo, 'get_by_email', return_value=user), \
             patch('src.core.security.password_handler.PasswordHandler.verify_password', return_value=True), \
             patch('src.core.security.password_handler.PasswordHandler.needs_rehash', return_value=True), \
             patch('src.core.security.password_handler.PasswordHandler.hash_password') as mock_hash, \
             patch.object(user_service.user_repo, 'update_password') as mock_update_password:
            
            user_service.authenticate_user(login_data)
            
            mock_hash.assert_called_once_with(password)
            mock_update_password.assert_called_once()

    def test_change_password_success(self, user_service: UserService):
        """Test successful password change."""
        current_password = "A_Strong_Password_123!"
        new_password = "A_New_Strong_Password_456!"
        hashed_password = PasswordHandler.hash_password(current_password)
        user = User(**{
            "id": 1,
            "name": "testuser",
            "email": "<EMAIL>",
            "password_hash": hashed_password,
            "is_active": True,
            "role": UserRole.ELECTRICAL_DESIGNER,
            "is_admin": False,
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00"
        })
        password_data = PasswordChangeRequestSchema(current_password=current_password, new_password=new_password, confirm_password=new_password)

        with patch.object(user_service.user_repo, 'get_by_id', return_value=user), \
             patch('src.core.security.password_handler.PasswordHandler.verify_password', return_value=True), \
             patch.object(user_service.user_repo, 'update_password', return_value=True):
            
            result = user_service.change_password(1, password_data)
            assert result is True
            user_service.user_repo.update_password.assert_called_once()

    def test_change_password_failure_wrong_current_password(self, user_service: UserService):
        """Test password change failure with an incorrect current password."""
        current_password = "A_Strong_Password_123!"
        new_password = "A_New_Strong_Password_456!"
        hashed_password = PasswordHandler.hash_password(current_password)
        user = User(**{
            "id": 1,
            "name": "testuser",
            "email": "<EMAIL>",
            "password_hash": hashed_password,
            "is_active": True,
            "role": UserRole.ELECTRICAL_DESIGNER,
            "is_admin": False,
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00"
        })
        password_data = PasswordChangeRequestSchema(current_password="wrong_password", new_password=new_password, confirm_password=new_password)

        with patch.object(user_service.user_repo, 'get_by_id', return_value=user), \
             patch('src.core.security.password_handler.PasswordHandler.verify_password', return_value=False):
            with pytest.raises(InvalidInputError, match="Current password is incorrect"):
                user_service.change_password(1, password_data)