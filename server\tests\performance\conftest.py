# tests/performance/conftest.py - Fixtures for performance tests
import pytest
from typing import List
from decimal import Decimal

from src.core.models.general.component import Component
from src.core.repositories.general.component_repository import ComponentRepository
from src.core.enums.electrical_enums import ComponentType, ComponentCategoryType


@pytest.fixture
def component_repository(db_session):
    """Create a component repository instance."""
    return ComponentRepository(db_session)


@pytest.fixture
def large_component_dataset(db_session, component_repository) -> List[Component]:
    """Create a large dataset of components for performance testing."""
    components = []
    
    # Create 1000 test components
    for i in range(1000):
        # Alternate between CIRCUIT_BREAKER and FUSE types
        component_type = ComponentType.CIRCUIT_BREAKER if i % 2 == 0 else ComponentType.FUSE
        component_data = {
            "name": f"Test Component {i:04d}",
            "manufacturer": f"Manufacturer {i % 10}",
            "model_number": f"MODEL-{i:04d}",
            "description": f"Test component {i} for performance testing",
            "component_type": component_type,
            "category": ComponentCategoryType.PROTECTION_DEVICES,
            "specifications": {
                "rated_current": 16 + (i % 50),
                "rated_voltage": 230 + (i % 100),
                "breaking_capacity": 6000 + (i % 1000),
                "curve_type": "C",
                "poles": 1 + (i % 3)
            },
            "unit_price": Decimal(str(100.0 + (i % 500))),
            "currency": "USD",
            "supplier": f"Supplier {i % 5}",
            "part_number": f"PART-{i:04d}",
            "weight_kg": 0.5 + (i % 10) * 0.1,
            "is_active": True,
            "is_preferred": i % 100 == 0,  # Every 100th component is preferred
            "stock_status": "available" if i % 10 != 9 else "limited",
            "version": "1.0"
        }
        
        component = component_repository.create(component_data)
        components.append(component)
        
        # Commit every 100 components to avoid memory issues
        if (i + 1) % 100 == 0:
            db_session.commit()
    
    # Final commit
    db_session.commit()
    
    return components


@pytest.fixture
def sample_component_data_list():
    """Generate a list of sample component data for bulk operations."""
    component_data_list = []
    
    for i in range(100):
        component_data = {
            "name": f"Bulk Component {i:03d}",
            "manufacturer": f"Bulk Manufacturer {i % 5}",
            "model_number": f"BULK-{i:03d}",
            "description": f"Bulk test component {i}",
            "component_type": ComponentType.CIRCUIT_BREAKER,
            "category": ComponentCategoryType.PROTECTION_DEVICES,
            "specifications": {
                "rated_current": 10 + (i % 20),
                "rated_voltage": 230,
                "breaking_capacity": 6000,
                "curve_type": "B",
                "poles": 1
            },
            "unit_price": Decimal(str(50.0 + i)),
            "currency": "USD",
            "supplier": f"Bulk Supplier {i % 3}",
            "part_number": f"BULK-PART-{i:03d}",
            "weight_kg": 0.3 + (i % 5) * 0.1,
            "is_active": True,
            "is_preferred": False,
            "stock_status": "available",
            "version": "1.0"
        }
        component_data_list.append(component_data)
    
    return component_data_list
