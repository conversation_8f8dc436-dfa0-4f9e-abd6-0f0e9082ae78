/**
 * ComponentSearch Unit Tests
 * Tests the ComponentSearch component with essential functionality
 */

import { screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders } from "@/test/utils"
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ComponentSearch } from '../../components/ComponentSearch'

// Mock the useComponentSuggestions hook
const mockSuggestions = ['resistor 1k', 'resistor 2k', 'resistor 10k']
vi.mock('../../api/componentQueries', () => ({
  useComponentSuggestions: vi.fn(() => ({
    data: mockSuggestions,
    isLoading: false,
    isError: false,
  })),
}))

describe('ComponentSearch', () => {
  const mockHandlers = {
    onSearch: vi.fn(),
    onChange: vi.fn(),
    onClear: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders search input with placeholder', () => {
      renderWithProviders(<ComponentSearch placeholder="Search components..." {...mockHandlers} />)

      const input = screen.getByPlaceholderText('Search components...')
      expect(input).toBeInTheDocument()
    })

    it('renders with initial value', () => {
      renderWithProviders(<ComponentSearch value="initial search" {...mockHandlers} />)

      const input = screen.getByDisplayValue('initial search')
      expect(input).toBeInTheDocument()
    })

    it('renders search icon', () => {
      renderWithProviders(<ComponentSearch {...mockHandlers} />)

      expect(screen.getByTestId('search-icon')).toBeInTheDocument()
    })

    it('renders clear button when there is text', () => {
      renderWithProviders(<ComponentSearch value="search text" {...mockHandlers} />)

      expect(screen.getByRole('button', { name: /clear search/i })).toBeInTheDocument()
    })

    it('does not render clear button when input is empty', () => {
      renderWithProviders(<ComponentSearch value="" {...mockHandlers} />)

      expect(screen.queryByRole('button', { name: /clear search/i })).not.toBeInTheDocument()
    })
  })

  describe('Input Interactions', () => {
    it('calls onChange when user types', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentSearch {...mockHandlers} />)

      const input = screen.getByRole('textbox')
      await user.type(input, 'resistor')

      expect(mockHandlers.onChange).toHaveBeenCalledWith('resistor')
    })

    it('calls onClear when clear button is clicked', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentSearch value="search text" {...mockHandlers} />)

      const clearButton = screen.getByRole('button', { name: /clear search/i })
      await user.click(clearButton)

      expect(mockHandlers.onClear).toHaveBeenCalled()
    })

    it('triggers search on Enter key press', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentSearch value="resistor" {...mockHandlers} />)

      const input = screen.getByRole('textbox')
      await user.type(input, '{enter}')

      expect(mockHandlers.onSearch).toHaveBeenCalledWith('resistor')
    })
  })

  describe('Suggestions Dropdown', () => {
    it('hides suggestions when showSuggestions is false', () => {
      renderWithProviders(<ComponentSearch showSuggestions={false} value="res" {...mockHandlers} />)

      expect(screen.queryByText('resistor 1k')).not.toBeInTheDocument()
    })

    it('shows recent searches when provided', () => {
      const recentSearches = ['capacitor', 'diode', 'transistor']

      renderWithProviders(<ComponentSearch recentSearches={recentSearches} {...mockHandlers} />)

      // Check that recent searches are available as prop
      expect(screen.getByRole('textbox')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      renderWithProviders(<ComponentSearch {...mockHandlers} />)

      const input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('type', 'text')
      expect(input).toHaveAttribute('placeholder')
    })
  })

  describe('Edge Cases', () => {
    it('handles missing handlers gracefully', () => {
      renderWithProviders(<ComponentSearch />)

      expect(screen.getByRole('textbox')).toBeInTheDocument()
    })

    it('handles empty value gracefully', () => {
      renderWithProviders(<ComponentSearch value="" {...mockHandlers} />)

      expect(screen.getByRole('textbox')).toHaveValue('')
    })

    it('handles null value gracefully', () => {
      renderWithProviders(<ComponentSearch value={undefined} {...mockHandlers} />)

      expect(screen.getByRole('textbox')).toBeInTheDocument()
    })

    it('handles different placeholder values', () => {
      renderWithProviders(<ComponentSearch placeholder="Custom placeholder" {...mockHandlers} />)

      expect(screen.getByPlaceholderText('Custom placeholder')).toBeInTheDocument()
    })

    it('handles different field types', () => {
      renderWithProviders(<ComponentSearch field="manufacturer" {...mockHandlers} />)

      expect(screen.getByRole('textbox')).toBeInTheDocument()
    })
  })
})