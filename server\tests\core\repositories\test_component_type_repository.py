
#!/usr/bin/env python3
"""Tests for Component Type functionality.

This module provides comprehensive tests for component type management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentType model validation and business logic
- ComponentTypeRepository data access operations
- ComponentTypeService business logic and validation
- Component Type API endpoints and error handling
- Category relationships and validation
- Specifications template management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_type import ComponentType
from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_type_repository import ComponentTypeRepository
from src.core.services.general.component_type_service import ComponentTypeService
from src.core.schemas.general.component_type_schemas import (
    ComponentTypeCreateSchema,
    ComponentTypeUpdateSchema,
    ComponentTypeSearchSchema,
)
from src.core.errors.exceptions import BusinessLogicError, NotFoundError, ValidationError
from src.core.utils.pagination_utils import PaginationParams


class TestComponentTypeRepository:
    """Test ComponentTypeRepository functionality."""


    def test_create_component_type(self, component_type_repository: ComponentTypeRepository, sample_category: ComponentCategory):
        """Test creating a component type through repository."""
        type_data = {
            "name": "New Type",
            "description": "New description",
            "category_id": sample_category.id,
            "is_active": True,
        }

        component_type = component_type_repository.create(type_data)

        assert component_type.id is not None
        assert component_type.name == "New Type"
        assert component_type.description == "New description"
        assert component_type.category_id == sample_category.id
        assert component_type.is_active is True

    def test_get_by_id(self, component_type_repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test retrieving component type by ID."""
        component_type = component_type_repository.get_by_id(sample_component_type.id)

        assert component_type is not None
        assert component_type.id == sample_component_type.id
        assert component_type.name == sample_component_type.name

    def test_get_by_name(self, component_type_repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test retrieving component type by name."""
        component_type = component_type_repository.get_by_name("Sample Type", sample_component_type.category_id)

        assert component_type is not None
        assert component_type.id == sample_component_type.id
        assert component_type.name == "Sample Type"

    def test_get_by_category(self, component_type_repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test retrieving component types by category."""
        types = component_type_repository.get_by_category(sample_component_type.category_id)

        assert len(types) >= 1
        assert any(t.id == sample_component_type.id for t in types)

    def test_search_types(self, component_type_repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test searching component types."""
        search_schema = ComponentTypeSearchSchema(
            search_term="Sample",
            category_id=sample_component_type.category_id,
            is_active=True,
            has_specifications_template=None,
            min_component_count=None,
            max_component_count=None
        )
        pagination = PaginationParams(page=1, per_page=10)
        
        types, total_count = component_type_repository.search_types(search_schema, pagination)
        
        assert total_count >= 1
        assert len(types) >= 1
        assert any(t.id == sample_component_type.id for t in types)

    def test_update_component_type(self, component_type_repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test updating a component type."""
        update_data = {
            "name": "Updated Type",
            "description": "Updated description",
        }
        
        updated_type = component_type_repository.update(sample_component_type.id, update_data)
        
        assert updated_type.name == "Updated Type"
        assert updated_type.description == "Updated description"

    def test_update_specifications_template(self, component_type_repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test updating specifications template."""
        template = {
            "electrical": {
                "voltage": {"type": "number", "required": True},
            }
        }
        
        updated_type = component_type_repository.update_specifications_template(sample_component_type.id, template)
        
        assert updated_type is not None
        assert updated_type.specifications_template == template

    def test_validate_category_exists(self, component_type_repository: ComponentTypeRepository, sample_category: ComponentCategory):
        """Test category validation."""
        # Test existing category
        assert component_type_repository.validate_category_exists(sample_category.id) is True

        # Test non-existing category
        assert component_type_repository.validate_category_exists(99999) is False
