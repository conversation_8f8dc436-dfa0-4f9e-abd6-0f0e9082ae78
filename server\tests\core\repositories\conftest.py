import pytest
from sqlalchemy.orm import Session
from decimal import Decimal
from typing import Any, List
import json

from src.core.repositories.general.component_repository import ComponentRepository
from src.core.repositories.general.component_category_repository import ComponentCategoryRepository
from src.core.repositories.general.component_type_repository import ComponentTypeRepository
from src.core.models.general.component import Component
from src.core.models.general.component_category import ComponentCategory
from src.core.models.general.component_type import ComponentType as ComponentTypeModel
from src.core.enums.electrical_enums import ComponentType, ComponentCategoryType

@pytest.fixture
def component_repository(db_session: Session) -> ComponentRepository:
    """Create a ComponentRepository instance for testing."""
    return ComponentRepository(db_session)

@pytest.fixture
def component_category_repository(db_session: Session) -> ComponentCategoryRepository:
    """Create ComponentCategoryRepository instance."""
    return ComponentCategoryRepository(db_session)

@pytest.fixture
def component_type_repository(db_session: Session) -> ComponentTypeRepository:
    """Create ComponentTypeRepository instance."""
    return ComponentTypeRepository(db_session)

@pytest.fixture
def sample_category(db_session: Session) -> ComponentCategory:
    """Create a sample category for testing."""
    category = ComponentCategory(
        name="Sample Category",
        description="Sample description",
        is_active=True,
    )
    db_session.add(category)
    db_session.commit()
    return category

@pytest.fixture
def sample_component_type(db_session: Session, sample_category: ComponentCategory) -> ComponentTypeModel:
    """Create a sample component type for testing."""
    component_type = ComponentTypeModel(
        name="Sample Type",
        description="Sample description",
        category_id=sample_category.id,
        is_active=True,
    )
    db_session.add(component_type)
    db_session.commit()
    return component_type

@pytest.fixture
def sample_component(db_session: Session):
    """Create a sample component for testing."""
    component = Component(
        name="Test Circuit Breaker",
        manufacturer="ABB",
        model_number="S203-B16",
        component_type=ComponentType.CIRCUIT_BREAKER,
        category=ComponentCategoryType.PROTECTION_DEVICES,
        unit_price=Decimal("45.50"),
        currency="EUR",
        supplier="RS Components",
        part_number="123-4567"
    )
    db_session.add(component)
    db_session.commit()
    return component

@pytest.fixture
def multiple_components(db_session: Session):
    """Create multiple components for testing."""
    components = [
        Component(
            name="Circuit Breaker 1",
            manufacturer="ABB",
            model_number="S203-B16",
            component_type=ComponentType.CIRCUIT_BREAKER,
            category=ComponentCategoryType.PROTECTION_DEVICES,
            unit_price=Decimal("45.50"),
            is_preferred=True
        ),
        Component(
            name="Circuit Breaker 2",
            manufacturer="Schneider Electric",
            model_number="C60N-B20",
            component_type=ComponentType.CIRCUIT_BREAKER,
            category=ComponentCategoryType.PROTECTION_DEVICES,
            unit_price=Decimal("52.00"),
            is_preferred=False
        ),
        Component(
            name="Motor 1",
            manufacturer="Siemens",
            model_number="1LA7090-4AA60",
            component_type=ComponentType.ELECTRIC_MOTOR,
            category=ComponentCategoryType.LOADS,
            unit_price=Decimal("1250.00"),
            is_preferred=True
        ),
        Component(
            name="Contactor 1",
            manufacturer="ABB",
            model_number="A26-30-10",
            component_type=ComponentType.CONTACTOR,
            category=ComponentCategoryType.SWITCHING_AND_CONTROL,
            unit_price=Decimal("85.00"),
            is_preferred=False
        )
    ]
    
    for component in components:
        db_session.add(component)
    db_session.commit()
    return components

@pytest.fixture(scope="function")
def sample_component_data() -> dict[str, Any]:
    """Sample component data for testing."""
    return {
        "name": "Test Circuit Breaker",
        "manufacturer": "ABB",
        "model_number": "S203-C16",
        "description": "3-pole miniature circuit breaker, 16A, C-curve",
        "component_type": ComponentType.CIRCUIT_BREAKER.value,
        "category": ComponentCategoryType.PROTECTION_DEVICES.value,
        "specifications": {
            "electrical": {
                "current_rating": "16A",
                "voltage_rating": "400V",
                "breaking_capacity": "6kA",
                "curve_type": "C"
            },
            "standards_compliance": ["IEC-60898-1", "EN-60898-1"]
        },
        "unit_price": "25.50",
        "currency": "EUR",
        "supplier": "Electrical Supplies Ltd",
        "part_number": "ABB-S203-C16",
        "weight_kg": 0.15,
        "dimensions": {
            "length": 18,
            "width": 85,
            "height": 78
        },
        "is_active": True,
        "is_preferred": False,
        "stock_status": "available",
        "version": "1.0",
        "metadata": {
            "test_data": True,
            "created_for": "unit_testing"
        }
    }

@pytest.fixture(scope="function")
def large_component_dataset(db_session):
    """Create a large dataset of components for performance testing."""
    components = []
    
    # Create 1000 components for performance testing
    for i in range(1000):
        component = Component(
            name=f"Component {i:04d}",
            manufacturer=f"Manufacturer {i % 10}",
            model_number=f"MODEL-{i:04d}",
            component_type=ComponentType.CIRCUIT_BREAKER if i % 2 == 0 else ComponentType.FUSE,
            category=ComponentCategoryType.PROTECTION_DEVICES,
            unit_price=Decimal(f"{10 + (i % 100)}.50"),
            currency="EUR",
            supplier=f"Supplier {i % 5}",
            part_number=f"PART-{i:04d}",
            description=f"Test component {i} for performance testing",
            specifications=json.dumps({
                "electrical": {
                    "voltage_rating": f"{400 + (i % 100)}V",
                    "current_rating": f"{16 + (i % 50)}A"
                },
                "physical": {
                    "width": f"{50 + (i % 20)}mm",
                    "height": f"{80 + (i % 30)}mm"
                }
            }),
            is_preferred=(i % 10 == 0),
            is_active=True
        )
        components.append(component)
    
    # Batch insert for better performance
    db_session.add_all(components)
    db_session.commit()

    return components


@pytest.fixture(scope="function")
def sample_component_category_data() -> dict[str, Any]:
    """Sample component category data for testing."""
    return {
        "name": "Test Protection Devices",
        "description": "Test category for protection devices",
        "parent_category_id": None,
        "is_active": True
    }


@pytest.fixture(scope="function")
def sample_component_type_data() -> dict[str, Any]:
    """Sample component type data for testing."""
    return {
        "name": "Test Circuit Breaker Type",
        "description": "Test type for circuit breakers",
        "category_id": 1,  # Will be set dynamically in tests
        "is_active": True,
        "specifications_template": {
            "electrical": {
                "current_rating": {"type": "string", "required": True},
                "voltage_rating": {"type": "string", "required": True},
                "breaking_capacity": {"type": "string", "required": False}
            },
            "physical": {
                "width": {"type": "number", "required": False},
                "height": {"type": "number", "required": False},
                "depth": {"type": "number", "required": False}
            }
        },
        "metadata": {
            "test_data": True,
            "created_for": "unit_testing"
        }
    }


@pytest.fixture(scope="function")
def component_category_fixture(db_session) -> ComponentCategory:
    """Create a test component category in the database."""
    category = ComponentCategory(
        name="Test Protection Devices",
        description="Test category for protection devices",
        parent_category_id=None,
        is_active=True
    )
    db_session.add(category)
    db_session.commit()
    db_session.refresh(category)
    return category


@pytest.fixture(scope="function")
def component_type_fixture(db_session, component_category_fixture) -> ComponentTypeModel:
    """Create a test component type in the database."""
    component_type = ComponentTypeModel(
        name="Test Circuit Breaker Type",
        description="Test type for circuit breakers",
        category_id=component_category_fixture.id,
        is_active=True,
        specifications_template=json.dumps({
            "electrical": {
                "current_rating": {"type": "string", "required": True},
                "voltage_rating": {"type": "string", "required": True},
                "breaking_capacity": {"type": "string", "required": False}
            }
        }),
        metadata_json=json.dumps({
            "test_data": True,
            "created_for": "unit_testing"
        })
    )
    db_session.add(component_type)
    db_session.commit()
    db_session.refresh(component_type)
    return component_type


@pytest.fixture(scope="function")
def multiple_component_categories(db_session) -> List[ComponentCategory]:
    """Create multiple test component categories for testing hierarchies."""
    categories = [
        ComponentCategory(
            name="Power Distribution",
            description="Power distribution components",
            parent_category_id=None,
            is_active=True
        ),
        ComponentCategory(
            name="Protection Devices",
            description="Electrical protection devices",
            parent_category_id=None,
            is_active=True
        ),
        ComponentCategory(
            name="Control Equipment",
            description="Control and automation equipment",
            parent_category_id=None,
            is_active=True
        )
    ]

    db_session.add_all(categories)
    db_session.commit()

    # Refresh to get IDs
    for category in categories:
        db_session.refresh(category)

    # Add subcategories
    subcategories = [
        ComponentCategory(
            name="Circuit Breakers",
            description="Circuit breaker subcategory",
            parent_category_id=categories[1].id,  # Protection Devices
            is_active=True
        ),
        ComponentCategory(
            name="Fuses",
            description="Fuse subcategory",
            parent_category_id=categories[1].id,  # Protection Devices
            is_active=True
        )
    ]

    db_session.add_all(subcategories)
    db_session.commit()

    for subcategory in subcategories:
        db_session.refresh(subcategory)

    return categories + subcategories


@pytest.fixture(scope="function")
def multiple_component_types(db_session, multiple_component_categories) -> List[ComponentTypeModel]:
    """Create multiple test component types for testing."""
    # Get the Protection Devices category
    protection_category = next(cat for cat in multiple_component_categories if cat.name == "Protection Devices")

    component_types = [
        ComponentTypeModel(
            name="Miniature Circuit Breaker",
            description="MCB for residential and commercial use",
            category_id=protection_category.id,
            is_active=True,
            specifications_template=json.dumps({
                "electrical": {
                    "current_rating": {"type": "string", "required": True},
                    "voltage_rating": {"type": "string", "required": True},
                    "curve_type": {"type": "string", "required": True}
                }
            })
        ),
        ComponentTypeModel(
            name="Molded Case Circuit Breaker",
            description="MCCB for industrial applications",
            category_id=protection_category.id,
            is_active=True,
            specifications_template=json.dumps({
                "electrical": {
                    "current_rating": {"type": "string", "required": True},
                    "voltage_rating": {"type": "string", "required": True},
                    "breaking_capacity": {"type": "string", "required": True}
                }
            })
        ),
        ComponentTypeModel(
            name="HRC Fuse",
            description="High rupturing capacity fuse",
            category_id=protection_category.id,
            is_active=True,
            specifications_template=json.dumps({
                "electrical": {
                    "current_rating": {"type": "string", "required": True},
                    "voltage_rating": {"type": "string", "required": True}
                }
            })
        )
    ]

    db_session.add_all(component_types)
    db_session.commit()

    for component_type in component_types:
        db_session.refresh(component_type)

    return component_types