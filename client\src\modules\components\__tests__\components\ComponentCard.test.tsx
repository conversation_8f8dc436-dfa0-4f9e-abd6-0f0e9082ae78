/**
 * ComponentCard Unit Tests
 * Tests the ComponentCard component with all props and interactions
 */

import { fireEvent, screen } from '@testing-library/react'
import { createMockComponent } from "@/test/factories/componentFactories"
import { mockComponent, renderWithProviders } from "@/test/utils"
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ComponentCard } from '../../components/ComponentCard'

// Mock the utility functions
vi.mock('../../utils', () => ({
  formatComponentName: vi.fn((component) => component.display_name || component.name),
  formatPrice: vi.fn((price, currency) => `€${price}`),
  formatWeight: vi.fn((weight) => `${weight} kg`),
  formatDimensions: vi.fn(
    (dimensions) => `${dimensions.length} × ${dimensions.width} × ${dimensions.height} mm`
  ),
  getComponentStatusColor: vi.fn(() => 'text-green-600'),
  getComponentStatusText: vi.fn(() => 'Available'),
}))

describe('ComponentCard', () => {
  const mockHandlers = {
    onSelect: vi.fn(),
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    onView: vi.fn(),
    onTogglePreferred: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders component information correctly', () => {
      renderWithProviders(<ComponentCard component={mockComponent} {...mockHandlers} />)

      // Check for the formatted component name (display_name)
      expect(screen.getByText(mockComponent.display_name || mockComponent.name)).toBeInTheDocument()
      // Check for manufacturer and model number in the subtitle
      expect(
        screen.getByText((content, element) => {
          return Boolean(
            element?.tagName === 'P' &&
            element?.className?.includes('text-gray-600') &&
            element?.textContent?.includes(mockComponent.manufacturer) &&
            element?.textContent?.includes(mockComponent.model_number)
          )
        })
      ).toBeInTheDocument()
      // Check for part number in the supplier information section
      expect(
        screen.getByText((content, element) => {
          return Boolean(
            element?.className?.toString().includes('text-gray-500') &&
            element?.textContent?.includes('Part:') &&
            element?.textContent?.includes(mockComponent.part_number || '')
          )
        })
      ).toBeInTheDocument()
    })

    it('renders with compact layout when compact prop is true', () => {
      renderWithProviders(
        <ComponentCard component={mockComponent} compact={true} {...mockHandlers} />
      )

      // In compact mode, some elements should have different styling
      const card = screen.getByRole('article')
      expect(card).toBeInTheDocument()
    })

    it('shows selected state when isSelected is true', () => {
      renderWithProviders(
        <ComponentCard component={mockComponent} isSelected={true} {...mockHandlers} />
      )

      const card = screen.getByRole('article')
      expect(card).toHaveClass('ring-2', 'ring-blue-500')
    })

    it('hides actions when showActions is false', () => {
      renderWithProviders(
        <ComponentCard component={mockComponent} showActions={false} {...mockHandlers} />
      )

      expect(screen.queryByRole('button', { name: /edit/i })).not.toBeInTheDocument()
      expect(screen.queryByRole('button', { name: /delete/i })).not.toBeInTheDocument()
    })

    it('displays preferred badge for preferred components', () => {
      const preferredComponent = createMockComponent({ is_preferred: true })

      renderWithProviders(<ComponentCard component={preferredComponent} {...mockHandlers} />)

      expect(screen.getByText('Preferred')).toBeInTheDocument()
    })

    it('displays inactive state for inactive components', () => {
      const inactiveComponent = createMockComponent({ is_active: false })

      renderWithProviders(<ComponentCard component={inactiveComponent} {...mockHandlers} />)

      expect(screen.getByText('Inactive')).toBeInTheDocument()
    })
  })

  describe('Interactions', () => {
    it('calls onSelect when card is clicked', () => {
      renderWithProviders(<ComponentCard component={mockComponent} {...mockHandlers} />)

      const card = screen.getByRole('article')
      fireEvent.click(card)

      expect(mockHandlers.onSelect).toHaveBeenCalledWith(mockComponent)
    })

    it('calls onEdit when edit button is clicked', () => {
      renderWithProviders(<ComponentCard component={mockComponent} {...mockHandlers} />)

      const editButton = screen.getByTestId(`edit-component-${mockComponent.id}`)
      fireEvent.click(editButton)

      expect(mockHandlers.onEdit).toHaveBeenCalledWith(mockComponent)
    })

    it('calls onDelete when delete button is clicked', () => {
      renderWithProviders(<ComponentCard component={mockComponent} {...mockHandlers} />)

      const deleteButton = screen.getByTestId(`delete-component-${mockComponent.id}`)
      fireEvent.click(deleteButton)

      expect(mockHandlers.onDelete).toHaveBeenCalledWith(mockComponent)
    })

    it('calls onView when view button is clicked', () => {
      renderWithProviders(<ComponentCard component={mockComponent} {...mockHandlers} />)

      const viewButton = screen.getByTestId(`view-component-${mockComponent.id}`)
      fireEvent.click(viewButton)

      expect(mockHandlers.onView).toHaveBeenCalledWith(mockComponent)
    })

    it('calls onTogglePreferred when preferred button is clicked', () => {
      renderWithProviders(<ComponentCard component={mockComponent} {...mockHandlers} />)

      const preferredButton = screen.getByRole('button', { name: /toggle preferred status/i })
      fireEvent.click(preferredButton)

      expect(mockHandlers.onTogglePreferred).toHaveBeenCalledWith(mockComponent)
    })

    it('prevents event propagation when action buttons are clicked', () => {
      const onSelect = vi.fn()

      renderWithProviders(
        <ComponentCard component={mockComponent} {...mockHandlers} onSelect={onSelect} />
      )

      const editButton = screen.getByTestId(`edit-component-${mockComponent.id}`)
      fireEvent.click(editButton)

      // onSelect should not be called when action button is clicked
      expect(onSelect).not.toHaveBeenCalled()
      expect(mockHandlers.onEdit).toHaveBeenCalledWith(mockComponent)
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      renderWithProviders(<ComponentCard component={mockComponent} {...mockHandlers} />)

      const card = screen.getByRole('article')
      expect(card).toHaveAttribute('aria-label', expect.stringContaining(mockComponent.display_name || mockComponent.name))
    })

    it('supports keyboard navigation', () => {
      renderWithProviders(<ComponentCard component={mockComponent} {...mockHandlers} />)

      const card = screen.getByRole('article')
      expect(card).toHaveAttribute('tabIndex', '0')

      fireEvent.keyDown(card, { key: 'Enter' })
      expect(mockHandlers.onSelect).toHaveBeenCalledWith(mockComponent)

      fireEvent.keyDown(card, { key: ' ' })
      expect(mockHandlers.onSelect).toHaveBeenCalledTimes(2)
    })

    it('has proper button labels for screen readers', () => {
      renderWithProviders(<ComponentCard component={mockComponent} {...mockHandlers} />)

      expect(screen.getByTestId(`edit-component-${mockComponent.id}`)).toHaveAttribute('aria-label')
      expect(screen.getByTestId(`delete-component-${mockComponent.id}`)).toHaveAttribute('aria-label')
      expect(screen.getByTestId(`view-component-${mockComponent.id}`)).toHaveAttribute('aria-label')
    })
  })

  describe('Edge Cases', () => {
    it('handles missing optional props gracefully', () => {
      renderWithProviders(<ComponentCard component={mockComponent} />)

      expect(screen.getByText(mockComponent.display_name || mockComponent.name)).toBeInTheDocument()
    })

    it('handles component with missing optional fields', () => {
      const minimalComponent = createMockComponent({
        display_name: undefined,
        description: undefined,
        unit_price: undefined,
      })

      renderWithProviders(<ComponentCard component={minimalComponent} {...mockHandlers} />)

      expect(screen.getByText(minimalComponent.name)).toBeInTheDocument()
    })

    it('handles very long component names', () => {
      const longNameComponent = createMockComponent({
        display_name: 'This is a very long component name that should be handled gracefully by the component card',
      })

      renderWithProviders(<ComponentCard component={longNameComponent} {...mockHandlers} />)

      expect(screen.getByText(longNameComponent.display_name || longNameComponent.name)).toBeInTheDocument()
    })

    it('handles components with no price', () => {
      const noPriceComponent = createMockComponent({
        unit_price: null,
      })

      renderWithProviders(<ComponentCard component={noPriceComponent} {...mockHandlers} />)

      expect(
        screen.getByText((content, element) => {
          return element?.textContent === 'Price: N/A' && element?.tagName === 'SPAN'
        })
      ).toBeInTheDocument()
    })
  })

  describe('Performance', () => {
    it('does not re-render unnecessarily', () => {
      const { rerender } = renderWithProviders(
        <ComponentCard component={mockComponent} {...mockHandlers} />
      )

      // Re-render with same props
      rerender(<ComponentCard component={mockComponent} {...mockHandlers} />)

      // Component should still be rendered correctly
      expect(screen.getByText(mockComponent.display_name || mockComponent.name)).toBeInTheDocument()
    })
  })
})
