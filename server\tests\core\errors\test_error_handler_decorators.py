# tests/unit/test_errors/test_error_handler_decorators.py
"""
Comprehensive tests for unified error handling system.

This module tests the unified error handler including decorators.
"""

import os
import sys
import logging
from unittest.mock import Mock, patch, MagicMock
from contextlib import contextmanager

import pytest
from fastapi import Request, HTTPException

# Add backend to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
sys.path.insert(0, server_path)

from src.core.errors.unified_error_handler import (
    ErrorContext,
    ErrorHandlingResult,
    UnifiedErrorHandler,
    unified_error_handler,
    get_unified_error_handler,
    handle_service_errors,
    handle_repository_errors,
    handle_calculation_errors,
)
from src.core.errors.exceptions import (
    BaseApplicationException,
    NotFoundError,
    DataValidationError,
    InvalidInputError,
    ServiceError,
    DatabaseError,
    CalculationError,
    DuplicateEntryError,
)

pytestmark = [pytest.mark.unit]


class TestErrorHandlerDecorators:
    """Test suite for error handler decorators."""

    def test_handle_service_errors_decorator_success(self):
        """Test handle_service_errors decorator with successful operation."""

        @handle_service_errors("test_service_operation")
        def successful_service_function():
            return "success"

        result = successful_service_function()
        assert result == "success"

    def test_handle_service_errors_decorator_with_application_exception(self):
        """Test handle_service_errors decorator with application exception."""

        @handle_service_errors("failing_service_operation")
        def failing_service_function():
            raise NotFoundError(code="404", detail="Resource not found")

        # Application exceptions should be re-raised as-is
        with pytest.raises(NotFoundError):
            failing_service_function()

    def test_handle_service_errors_decorator_with_generic_exception(self):
        """Test handle_service_errors decorator with generic exception."""

        @handle_service_errors("generic_error_operation")
        def generic_error_function():
            raise ValueError("Generic error")

        # Generic exceptions should be converted to ServiceError
        with pytest.raises(ServiceError) as exc_info:
            generic_error_function()

        assert "Service operation failed" in str(exc_info.value.detail)

    def test_handle_repository_errors_decorator_success(self):
        """Test handle_repository_errors decorator with successful operation."""

        @handle_repository_errors("user")
        def successful_repository_function():
            return {"id": 1, "name": "test"}

        result = successful_repository_function()
        assert result["name"] == "test"

    def test_handle_repository_errors_decorator_with_application_exception(self):
        """Test handle_repository_errors decorator with application exception."""

        @handle_repository_errors("user")
        def failing_repository_function():
            raise DatabaseError("Database connection failed")

        # Application exceptions should be re-raised as-is
        with pytest.raises(DatabaseError):
            failing_repository_function()

    def test_handle_repository_errors_decorator_with_integrity_error(self):
        """Test handle_repository_errors decorator with SQLAlchemy IntegrityError."""
        from sqlalchemy.exc import IntegrityError

        @handle_repository_errors("user")
        def integrity_error_function():
            # Mock IntegrityError
            raise IntegrityError("statement", "params", "orig")

        # Should convert to DuplicateEntryError
        with pytest.raises(DuplicateEntryError) as exc_info:
            integrity_error_function()

        assert "A user with the given unique constraint already exists" in str(
            exc_info.value.detail
        )

    def test_handle_repository_errors_decorator_with_no_result_found(self):
        """Test handle_repository_errors decorator with SQLAlchemy NoResultFound."""
        from sqlalchemy.exc import NoResultFound

        @handle_repository_errors("project")
        def no_result_function():
            raise NoResultFound("No result found")

        # Should convert to NotFoundError
        with pytest.raises(NotFoundError) as exc_info:
            no_result_function()

        assert "Project not found" in str(exc_info.value.detail)
        assert exc_info.value.code == "PROJECT_NOT_FOUND"

    def test_handle_repository_errors_decorator_with_generic_sqlalchemy_error(self):
        """Test handle_repository_errors decorator with generic SQLAlchemy error."""
        from sqlalchemy.exc import SQLAlchemyError

        @handle_repository_errors("component")
        def sqlalchemy_error_function():
            raise SQLAlchemyError("Generic SQLAlchemy error")

        # Should convert to DatabaseError
        with pytest.raises(DatabaseError) as exc_info:
            sqlalchemy_error_function()

        assert "Database operation failed for component" in str(exc_info.value.detail)

    def test_handle_repository_errors_decorator_with_generic_exception(self):
        """Test handle_repository_errors decorator with generic exception."""

        @handle_repository_errors("entity")
        def generic_error_function():
            raise ConnectionError("Connection failed")

        # Generic exceptions should be converted to ServiceError
        with pytest.raises(ServiceError) as exc_info:
            generic_error_function()

        assert "Repository operation failed" in str(exc_info.value.detail)

    def test_handle_calculation_errors_decorator_success(self):
        """Test handle_calculation_errors decorator with successful operation."""

        @handle_calculation_errors("heat_loss")
        def successful_calculation():
            return {"result": 42.5, "unit": "W"}

        result = successful_calculation()
        assert result["result"] == 42.5

    def test_handle_calculation_errors_decorator_with_exception(self):
        """Test handle_calculation_errors decorator with exception."""

        @handle_calculation_errors("power_calculation")
        def failing_calculation():
            raise ZeroDivisionError("Division by zero")

        # Should convert to CalculationError
        with pytest.raises(CalculationError) as exc_info:
            failing_calculation()

        assert "Calculation failed" in str(exc_info.value.detail)

    def test_decorator_function_metadata_preservation(self):
        """Test that decorators preserve function metadata."""

        @handle_service_errors("test_operation")
        def test_function():
            """Test function docstring."""
            return "test"

        # Function name should be preserved (though wrapper might change it)
        result = test_function()
        assert result == "test"

    def test_decorator_with_function_arguments(self):
        """Test decorators work with functions that have arguments."""

        @handle_service_errors("parameterized_operation")
        def parameterized_function(x, y, z=None):
            if z is None:
                return x + y
            return x + y + z

        # Test with positional arguments
        result1 = parameterized_function(1, 2)
        assert result1 == 3

        # Test with keyword arguments
        result2 = parameterized_function(1, 2, z=3)
        assert result2 == 6

    def test_decorator_error_context_tracking(self):
        """Test that decorators properly track error context."""
        # Clear any existing error history
        unified_error_handler.clear_error_history()

        @handle_service_errors("tracked_operation")
        def tracked_function():
            raise ValueError("Tracked error")

        with pytest.raises(ServiceError):
            tracked_function()

        # Check that error was tracked with proper context
        stats = unified_error_handler.get_error_statistics()
        assert stats["error_counts"]["total"] >= 1

        # Find our error in the history
        service_errors = [
            error
            for error in unified_error_handler.error_history
            if error["context_data"].get("operation") == "tracked_operation"
        ]
        assert len(service_errors) >= 1

    def test_nested_decorators(self):
        """Test nested error handling decorators."""

        @handle_service_errors("outer_service")
        def outer_function():
            return inner_function()

        @handle_repository_errors("inner_entity")
        def inner_function():
            raise ValueError("Inner error")

        # Should handle the error at the repository level first
        with pytest.raises(ServiceError):
            outer_function()

    def test_decorator_operation_name_defaults(self):
        """Test decorator operation name defaults."""

        @handle_service_errors()  # No operation name provided
        def default_name_function():
            raise ValueError("Default name test")

        # Should use default operation name
        with pytest.raises(ServiceError):
            default_name_function()

    def test_decorator_entity_name_defaults(self):
        """Test repository decorator entity name defaults."""

        @handle_repository_errors()  # No entity name provided
        def default_entity_function():
            raise ValueError("Default entity test")

        # Should use default entity name "resource"
        with pytest.raises(ServiceError):
            default_entity_function()

    def test_calculation_decorator_type_defaults(self):
        """Test calculation decorator type defaults."""

        @handle_calculation_errors()  # No calculation type provided
        def default_calculation_function():
            raise ValueError("Default calculation test")

        # Should use default calculation type "calculation"
        with pytest.raises(CalculationError):
            default_calculation_function()
