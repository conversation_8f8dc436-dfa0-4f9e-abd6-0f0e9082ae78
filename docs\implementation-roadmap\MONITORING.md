# Monitoring and Observability
## Ultimate Electrical Designer Application

### Document Information
- **Version**: 1.0
- **Date**: July 2025
- **Scope**: Comprehensive monitoring and observability
- **Tools**: Prometheus, Grafana, EL<PERSON> Stack, Jaeger
- **Standards**: SRE and observability best practices

---

## 1. Executive Summary

This document establishes comprehensive monitoring and observability for the Ultimate Electrical Designer application. The monitoring framework ensures proactive issue detection, performance optimization, and operational excellence through metrics, logs, traces, and alerting systems.

### 1.1 Observability Architecture
- **Metrics Collection**: Prometheus with custom metrics
- **Visualization**: Grafana dashboards and alerts
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Distributed Tracing**: <PERSON><PERSON><PERSON> for request tracing
- **Alerting**: PagerDuty and Slack integration

### 1.2 Monitoring Objectives
- **99.9% Uptime**: Proactive monitoring and alerting
- **Sub-200ms Response Time**: Performance monitoring and optimization
- **Real-time Visibility**: Complete system observability
- **Professional Standards**: Electrical engineering calculation monitoring
- **Compliance Tracking**: Regulatory and professional compliance monitoring

### 1.3 Current Implementation Status
- **Metrics Collection**: Prometheus deployed and configured
- **Dashboards**: Grafana dashboards operational
- **Logging**: ELK Stack configured and ingesting logs
- **Tracing**: Jaeger tracing implemented
- **Alerting**: Alert rules configured with escalation

---

## 2. Metrics Collection and Monitoring

### 2.1 Application Metrics

#### 2.1.1 Custom Metrics Implementation
```python
# server/src/core/monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge, Info
import time
import functools
from typing import Callable, Any

# Application metrics
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code', 'user_type']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint', 'status_code'],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0, 7.5, 10.0]
)

ACTIVE_CONNECTIONS = Gauge(
    'active_connections',
    'Number of active connections',
    ['connection_type']
)

CALCULATION_COUNT = Counter(
    'electrical_calculations_total',
    'Total electrical calculations performed',
    ['calculation_type', 'standards_applied', 'user_type', 'status']
)

CALCULATION_DURATION = Histogram(
    'electrical_calculation_duration_seconds',
    'Electrical calculation duration',
    ['calculation_type', 'complexity'],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0]
)

CALCULATION_ACCURACY = Gauge(
    'calculation_accuracy_percentage',
    'Calculation accuracy percentage',
    ['calculation_type', 'standards_applied']
)

PE_VALIDATIONS = Counter(
    'pe_validations_total',
    'Total PE validations',
    ['validation_type', 'status', 'state']
)

DATABASE_CONNECTIONS = Gauge(
    'database_connections',
    'Number of database connections',
    ['connection_pool', 'status']
)

CACHE_OPERATIONS = Counter(
    'cache_operations_total',
    'Total cache operations',
    ['operation', 'result']
)

CACHE_HIT_RATIO = Gauge(
    'cache_hit_ratio',
    'Cache hit ratio',
    ['cache_type']
)

ERROR_COUNT = Counter(
    'application_errors_total',
    'Total application errors',
    ['error_type', 'severity', 'component']
)

PROFESSIONAL_VALIDATIONS = Counter(
    'professional_validations_total',
    'Total professional validations',
    ['validation_type', 'status', 'standards']
)

class MetricsCollector:
    """
    Comprehensive metrics collection for Ultimate Electrical Designer
    
    Features:
    - Custom application metrics
    - Professional engineering metrics
    - Performance monitoring
    - Error tracking
    """
    
    def __init__(self):
        self.app_info = Info('ultimate_electrical_designer_info', 'Application information')
        self.app_info.info({
            'version': '1.0.0',
            'environment': 'production',
            'standards': 'IEEE,IEC,EN,NFPA'
        })
    
    def track_http_request(self, method: str, endpoint: str, status_code: int, 
                          duration: float, user_type: str = 'unknown'):
        """Track HTTP request metrics"""
        REQUEST_COUNT.labels(
            method=method,
            endpoint=endpoint,
            status_code=status_code,
            user_type=user_type
        ).inc()
        
        REQUEST_DURATION.labels(
            method=method,
            endpoint=endpoint,
            status_code=status_code
        ).observe(duration)
    
    def track_electrical_calculation(self, calculation_type: str, duration: float,
                                   standards_applied: str, user_type: str,
                                   status: str, accuracy: float = None):
        """Track electrical calculation metrics"""
        CALCULATION_COUNT.labels(
            calculation_type=calculation_type,
            standards_applied=standards_applied,
            user_type=user_type,
            status=status
        ).inc()
        
        # Determine complexity based on calculation type
        complexity = self._determine_complexity(calculation_type)
        
        CALCULATION_DURATION.labels(
            calculation_type=calculation_type,
            complexity=complexity
        ).observe(duration)
        
        if accuracy is not None:
            CALCULATION_ACCURACY.labels(
                calculation_type=calculation_type,
                standards_applied=standards_applied
            ).set(accuracy)
    
    def track_pe_validation(self, validation_type: str, status: str, state: str):
        """Track PE validation metrics"""
        PE_VALIDATIONS.labels(
            validation_type=validation_type,
            status=status,
            state=state
        ).inc()
    
    def track_database_connections(self, pool_name: str, active: int, idle: int):
        """Track database connection metrics"""
        DATABASE_CONNECTIONS.labels(
            connection_pool=pool_name,
            status='active'
        ).set(active)
        
        DATABASE_CONNECTIONS.labels(
            connection_pool=pool_name,
            status='idle'
        ).set(idle)
    
    def track_cache_operation(self, operation: str, result: str):
        """Track cache operation metrics"""
        CACHE_OPERATIONS.labels(
            operation=operation,
            result=result
        ).inc()
    
    def update_cache_hit_ratio(self, cache_type: str, hit_ratio: float):
        """Update cache hit ratio"""
        CACHE_HIT_RATIO.labels(cache_type=cache_type).set(hit_ratio)
    
    def track_error(self, error_type: str, severity: str, component: str):
        """Track application errors"""
        ERROR_COUNT.labels(
            error_type=error_type,
            severity=severity,
            component=component
        ).inc()
    
    def track_professional_validation(self, validation_type: str, status: str, standards: str):
        """Track professional validation metrics"""
        PROFESSIONAL_VALIDATIONS.labels(
            validation_type=validation_type,
            status=status,
            standards=standards
        ).inc()
    
    def _determine_complexity(self, calculation_type: str) -> str:
        """Determine calculation complexity"""
        complex_calculations = ['short_circuit', 'arc_flash', 'load_flow', 'harmonic_analysis']
        if calculation_type in complex_calculations:
            return 'complex'
        elif calculation_type in ['voltage_drop', 'power_factor_correction']:
            return 'medium'
        else:
            return 'simple'

# Global metrics collector instance
metrics_collector = MetricsCollector()

def track_performance(calculation_type: str = None, standards: str = None):
    """Decorator to track function performance"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                if calculation_type:
                    metrics_collector.track_electrical_calculation(
                        calculation_type=calculation_type,
                        duration=duration,
                        standards_applied=standards or 'IEEE',
                        user_type='engineer',
                        status='success'
                    )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics_collector.track_error(
                    error_type=type(e).__name__,
                    severity='error',
                    component=func.__module__
                )
                raise
        return wrapper
    return decorator
```

#### 2.1.2 FastAPI Metrics Middleware
```python
# server/src/middleware/metrics_middleware.py
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from src.core.monitoring.metrics import metrics_collector
import time

class MetricsMiddleware(BaseHTTPMiddleware):
    """
    Middleware to collect HTTP request metrics
    """
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # Track active connections
        from src.core.monitoring.metrics import ACTIVE_CONNECTIONS
        ACTIVE_CONNECTIONS.labels(connection_type='http').inc()
        
        try:
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Determine user type
            user_type = self._get_user_type(request)
            
            # Track metrics
            metrics_collector.track_http_request(
                method=request.method,
                endpoint=str(request.url.path),
                status_code=response.status_code,
                duration=duration,
                user_type=user_type
            )
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            
            # Track error
            metrics_collector.track_error(
                error_type=type(e).__name__,
                severity='error',
                component='http_middleware'
            )
            
            # Track failed request
            metrics_collector.track_http_request(
                method=request.method,
                endpoint=str(request.url.path),
                status_code=500,
                duration=duration,
                user_type=self._get_user_type(request)
            )
            
            raise
        finally:
            # Track connection closed
            ACTIVE_CONNECTIONS.labels(connection_type='http').dec()
    
    def _get_user_type(self, request: Request) -> str:
        """Determine user type from request"""
        # Check for PE validation token or role
        auth_header = request.headers.get('Authorization')
        if auth_header:
            # Parse JWT token to determine user type
            # This is a simplified example
            if 'pe_license' in str(auth_header):
                return 'professional_engineer'
            elif 'admin' in str(auth_header):
                return 'admin'
            else:
                return 'engineer'
        return 'anonymous'
```

### 2.2 Infrastructure Metrics

#### 2.2.1 Kubernetes Metrics Collection
```yaml
# infrastructure/monitoring/kube-state-metrics.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kube-state-metrics
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kube-state-metrics
  template:
    metadata:
      labels:
        app: kube-state-metrics
    spec:
      containers:
        - name: kube-state-metrics
          image: quay.io/coreos/kube-state-metrics:v2.9.2
          ports:
            - containerPort: 8080
              name: http-metrics
            - containerPort: 8081
              name: telemetry
          resources:
            requests:
              memory: "64Mi"
              cpu: "10m"
            limits:
              memory: "128Mi"
              cpu: "100m"
          securityContext:
            runAsNonRoot: true
            runAsUser: 65534
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL

---
apiVersion: v1
kind: Service
metadata:
  name: kube-state-metrics
  namespace: monitoring
spec:
  selector:
    app: kube-state-metrics
  ports:
    - name: http-metrics
      port: 8080
      targetPort: 8080
    - name: telemetry
      port: 8081
      targetPort: 8081
```

#### 2.2.2 Node Exporter Configuration
```yaml
# infrastructure/monitoring/node-exporter.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: node-exporter
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: node-exporter
  template:
    metadata:
      labels:
        app: node-exporter
    spec:
      hostNetwork: true
      hostPID: true
      containers:
        - name: node-exporter
          image: prom/node-exporter:latest
          ports:
            - containerPort: 9100
              name: metrics
          volumeMounts:
            - name: proc
              mountPath: /host/proc
              readOnly: true
            - name: sys
              mountPath: /host/sys
              readOnly: true
            - name: rootfs
              mountPath: /rootfs
              readOnly: true
          args:
            - '--path.procfs=/host/proc'
            - '--path.sysfs=/host/sys'
            - '--path.rootfs=/rootfs'
            - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
          resources:
            requests:
              memory: "64Mi"
              cpu: "10m"
            limits:
              memory: "128Mi"
              cpu: "100m"
          securityContext:
            runAsNonRoot: true
            runAsUser: 65534
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
      volumes:
        - name: proc
          hostPath:
            path: /proc
        - name: sys
          hostPath:
            path: /sys
        - name: rootfs
          hostPath:
            path: /
```

---

## 3. Logging and Log Management

### 3.1 Structured Logging Implementation

#### 3.1.1 Application Logging Configuration
```python
# server/src/core/logging/logger.py
import logging
import json
import sys
from datetime import datetime
from typing import Dict, Any, Optional
from pythonjsonlogger import jsonlogger

class ElectricalDesignerLogger:
    """
    Structured logging for Ultimate Electrical Designer
    
    Features:
    - JSON structured logging
    - Professional engineering context
    - Compliance tracking
    - Performance monitoring
    """
    
    def __init__(self, name: str, level: str = 'INFO'):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # JSON formatter
        json_formatter = jsonlogger.JsonFormatter(
            fmt='%(asctime)s %(name)s %(levelname)s %(message)s',
            datefmt='%Y-%m-%dT%H:%M:%S'
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(json_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler for audit trail
        file_handler = logging.FileHandler('/var/log/electrical-designer/audit.log')
        file_handler.setFormatter(json_formatter)
        self.logger.addHandler(file_handler)
    
    def log_calculation(self, calculation_type: str, user_id: str, 
                       project_id: str, duration: float, 
                       accuracy: float, standards: str,
                       pe_validated: bool = False, **kwargs):
        """Log electrical calculation"""
        self.logger.info(
            "Electrical calculation performed",
            extra={
                'event_type': 'calculation',
                'calculation_type': calculation_type,
                'user_id': user_id,
                'project_id': project_id,
                'duration_seconds': duration,
                'accuracy_percentage': accuracy,
                'standards_applied': standards,
                'pe_validated': pe_validated,
                'timestamp': datetime.utcnow().isoformat(),
                **kwargs
            }
        )
    
    def log_pe_validation(self, validation_type: str, pe_license: str,
                         calculation_id: str, validation_result: str,
                         reviewer_comments: str = None):
        """Log PE validation"""
        self.logger.info(
            "PE validation performed",
            extra={
                'event_type': 'pe_validation',
                'validation_type': validation_type,
                'pe_license': pe_license,
                'calculation_id': calculation_id,
                'validation_result': validation_result,
                'reviewer_comments': reviewer_comments,
                'timestamp': datetime.utcnow().isoformat(),
                'compliance_audit': True
            }
        )
    
    def log_standards_compliance(self, standards: str, compliance_type: str,
                               entity_id: str, compliance_status: str,
                               validation_details: Dict[str, Any]):
        """Log standards compliance"""
        self.logger.info(
            "Standards compliance check",
            extra={
                'event_type': 'standards_compliance',
                'standards': standards,
                'compliance_type': compliance_type,
                'entity_id': entity_id,
                'compliance_status': compliance_status,
                'validation_details': validation_details,
                'timestamp': datetime.utcnow().isoformat(),
                'compliance_audit': True
            }
        )
    
    def log_performance(self, operation: str, duration: float,
                       resource_usage: Dict[str, Any],
                       performance_metrics: Dict[str, Any]):
        """Log performance metrics"""
        self.logger.info(
            "Performance measurement",
            extra={
                'event_type': 'performance',
                'operation': operation,
                'duration_seconds': duration,
                'resource_usage': resource_usage,
                'performance_metrics': performance_metrics,
                'timestamp': datetime.utcnow().isoformat()
            }
        )
    
    def log_error(self, error_type: str, error_message: str,
                 stack_trace: str, context: Dict[str, Any] = None):
        """Log application errors"""
        self.logger.error(
            "Application error occurred",
            extra={
                'event_type': 'error',
                'error_type': error_type,
                'error_message': error_message,
                'stack_trace': stack_trace,
                'context': context or {},
                'timestamp': datetime.utcnow().isoformat()
            }
        )
    
    def log_security_event(self, event_type: str, user_id: str,
                          ip_address: str, user_agent: str,
                          security_details: Dict[str, Any]):
        """Log security events"""
        self.logger.warning(
            "Security event detected",
            extra={
                'event_type': 'security',
                'security_event_type': event_type,
                'user_id': user_id,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'security_details': security_details,
                'timestamp': datetime.utcnow().isoformat(),
                'security_audit': True
            }
        )
    
    def log_compliance_audit(self, audit_type: str, entity_type: str,
                           entity_id: str, audit_result: str,
                           auditor_id: str, audit_details: Dict[str, Any]):
        """Log compliance audit events"""
        self.logger.info(
            "Compliance audit performed",
            extra={
                'event_type': 'compliance_audit',
                'audit_type': audit_type,
                'entity_type': entity_type,
                'entity_id': entity_id,
                'audit_result': audit_result,
                'auditor_id': auditor_id,
                'audit_details': audit_details,
                'timestamp': datetime.utcnow().isoformat(),
                'compliance_audit': True
            }
        )

# Global logger instances
app_logger = ElectricalDesignerLogger('ultimate_electrical_designer')
calculation_logger = ElectricalDesignerLogger('electrical_calculations')
compliance_logger = ElectricalDesignerLogger('compliance_monitoring')
security_logger = ElectricalDesignerLogger('security_monitoring')
```

### 3.2 ELK Stack Configuration

#### 3.2.1 Logstash Configuration
```yaml
# infrastructure/logging/logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "ultimate-electrical-designer" {
    json {
      source => "message"
    }
    
    if [event_type] == "calculation" {
      mutate {
        add_tag => ["electrical_calculation"]
      }
    }
    
    if [event_type] == "pe_validation" {
      mutate {
        add_tag => ["professional_validation"]
      }
    }
    
    if [event_type] == "standards_compliance" {
      mutate {
        add_tag => ["compliance_check"]
      }
    }
    
    if [event_type] == "error" {
      mutate {
        add_tag => ["application_error"]
      }
    }
    
    if [event_type] == "security" {
      mutate {
        add_tag => ["security_event"]
      }
    }
    
    if [compliance_audit] == true {
      mutate {
        add_tag => ["compliance_audit"]
      }
    }
    
    # Parse timestamp
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    # Add environment information
    mutate {
      add_field => {
        "environment" => "${ENVIRONMENT:production}"
        "service" => "ultimate-electrical-designer"
        "version" => "${SERVICE_VERSION:1.0.0}"
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "ultimate-electrical-designer-%{+YYYY.MM.dd}"
    template_name => "ultimate-electrical-designer"
    template_pattern => "ultimate-electrical-designer-*"
    template => "/usr/share/logstash/templates/electrical-designer-template.json"
  }
  
  # Send security events to separate index
  if "security_event" in [tags] {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "security-events-%{+YYYY.MM.dd}"
    }
  }
  
  # Send compliance audit events to separate index
  if "compliance_audit" in [tags] {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "compliance-audit-%{+YYYY.MM.dd}"
    }
  }
}
```

#### 3.2.2 Elasticsearch Index Template
```json
{
  "template": "ultimate-electrical-designer-*",
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 1,
    "index.refresh_interval": "5s",
    "index.mapping.total_fields.limit": 2000
  },
  "mappings": {
    "properties": {
      "@timestamp": {
        "type": "date"
      },
      "timestamp": {
        "type": "date"
      },
      "event_type": {
        "type": "keyword"
      },
      "calculation_type": {
        "type": "keyword"
      },
      "user_id": {
        "type": "keyword"
      },
      "project_id": {
        "type": "keyword"
      },
      "duration_seconds": {
        "type": "float"
      },
      "accuracy_percentage": {
        "type": "float"
      },
      "standards_applied": {
        "type": "keyword"
      },
      "pe_validated": {
        "type": "boolean"
      },
      "pe_license": {
        "type": "keyword"
      },
      "validation_result": {
        "type": "keyword"
      },
      "compliance_status": {
        "type": "keyword"
      },
      "compliance_audit": {
        "type": "boolean"
      },
      "security_audit": {
        "type": "boolean"
      },
      "error_type": {
        "type": "keyword"
      },
      "error_message": {
        "type": "text"
      },
      "stack_trace": {
        "type": "text"
      },
      "ip_address": {
        "type": "ip"
      },
      "user_agent": {
        "type": "text"
      },
      "environment": {
        "type": "keyword"
      },
      "service": {
        "type": "keyword"
      },
      "version": {
        "type": "keyword"
      }
    }
  }
}
```

---

## 4. Distributed Tracing

### 4.1 OpenTelemetry Implementation

#### 4.1.1 Tracing Configuration
```python
# server/src/core/tracing/tracer.py
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.resources import Resource
import os

class ElectricalDesignerTracer:
    """
    Distributed tracing for Ultimate Electrical Designer
    
    Features:
    - Request tracing across services
    - Database query tracing
    - Cache operation tracing
    - Professional validation tracing
    """
    
    def __init__(self):
        # Configure resource
        resource = Resource(attributes={
            "service.name": "ultimate-electrical-designer",
            "service.version": "1.0.0",
            "service.namespace": "electrical-engineering",
            "deployment.environment": os.getenv("ENVIRONMENT", "production")
        })
        
        # Configure tracer provider
        trace.set_tracer_provider(TracerProvider(resource=resource))
        
        # Configure Jaeger exporter
        jaeger_exporter = JaegerExporter(
            agent_host_name=os.getenv("JAEGER_HOST", "localhost"),
            agent_port=int(os.getenv("JAEGER_PORT", "14268")),
            collector_endpoint=os.getenv("JAEGER_ENDPOINT", "http://jaeger:14268/api/traces")
        )
        
        # Configure span processor
        span_processor = BatchSpanProcessor(jaeger_exporter)
        trace.get_tracer_provider().add_span_processor(span_processor)
        
        # Get tracer
        self.tracer = trace.get_tracer(__name__)
        
        # Instrument frameworks
        self._instrument_frameworks()
    
    def _instrument_frameworks(self):
        """Instrument common frameworks"""
        # FastAPI instrumentation
        FastAPIInstrumentor.instrument()
        
        # SQLAlchemy instrumentation
        SQLAlchemyInstrumentor.instrument()
        
        # Redis instrumentation
        RedisInstrumentor.instrument()
        
        # Requests instrumentation
        RequestsInstrumentor.instrument()
    
    def trace_electrical_calculation(self, calculation_type: str, project_id: str,
                                   user_id: str, standards: str):
        """Context manager for tracing electrical calculations"""
        return self.tracer.start_as_current_span(
            f"electrical_calculation.{calculation_type}",
            attributes={
                "calculation.type": calculation_type,
                "project.id": project_id,
                "user.id": user_id,
                "standards.applied": standards,
                "service.component": "calculation_engine"
            }
        )
    
    def trace_pe_validation(self, validation_type: str, pe_license: str,
                          calculation_id: str):
        """Context manager for tracing PE validations"""
        return self.tracer.start_as_current_span(
            f"pe_validation.{validation_type}",
            attributes={
                "validation.type": validation_type,
                "pe.license": pe_license,
                "calculation.id": calculation_id,
                "service.component": "professional_validation"
            }
        )
    
    def trace_standards_compliance(self, standards: str, compliance_type: str,
                                 entity_id: str):
        """Context manager for tracing standards compliance"""
        return self.tracer.start_as_current_span(
            f"standards_compliance.{compliance_type}",
            attributes={
                "standards.name": standards,
                "compliance.type": compliance_type,
                "entity.id": entity_id,
                "service.component": "compliance_validator"
            }
        )
    
    def trace_database_operation(self, operation: str, table: str):
        """Context manager for tracing database operations"""
        return self.tracer.start_as_current_span(
            f"database.{operation}",
            attributes={
                "db.operation": operation,
                "db.table": table,
                "service.component": "database"
            }
        )
    
    def trace_cache_operation(self, operation: str, cache_key: str):
        """Context manager for tracing cache operations"""
        return self.tracer.start_as_current_span(
            f"cache.{operation}",
            attributes={
                "cache.operation": operation,
                "cache.key": cache_key,
                "service.component": "cache"
            }
        )
    
    def add_calculation_attributes(self, span, calculation_result):
        """Add calculation-specific attributes to span"""
        if hasattr(calculation_result, 'accuracy'):
            span.set_attribute("calculation.accuracy", calculation_result.accuracy)
        if hasattr(calculation_result, 'duration'):
            span.set_attribute("calculation.duration", calculation_result.duration)
        if hasattr(calculation_result, 'complexity'):
            span.set_attribute("calculation.complexity", calculation_result.complexity)
    
    def add_error_attributes(self, span, error):
        """Add error attributes to span"""
        span.set_attribute("error.type", type(error).__name__)
        span.set_attribute("error.message", str(error))
        span.set_status(trace.Status(trace.StatusCode.ERROR, str(error)))

# Global tracer instance
electrical_tracer = ElectricalDesignerTracer()
```

#### 4.1.2 Tracing Decorator
```python
# server/src/core/tracing/decorators.py
import functools
from typing import Callable, Any
from src.core.tracing.tracer import electrical_tracer

def trace_calculation(calculation_type: str, standards: str = "IEEE"):
    """Decorator to trace electrical calculations"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # Extract context from arguments
            project_id = kwargs.get('project_id', 'unknown')
            user_id = kwargs.get('user_id', 'unknown')
            
            with electrical_tracer.trace_electrical_calculation(
                calculation_type=calculation_type,
                project_id=project_id,
                user_id=user_id,
                standards=standards
            ) as span:
                try:
                    result = func(*args, **kwargs)
                    
                    # Add calculation-specific attributes
                    electrical_tracer.add_calculation_attributes(span, result)
                    
                    return result
                except Exception as e:
                    electrical_tracer.add_error_attributes(span, e)
                    raise
        return wrapper
    return decorator

def trace_pe_validation(validation_type: str):
    """Decorator to trace PE validations"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            pe_license = kwargs.get('pe_license', 'unknown')
            calculation_id = kwargs.get('calculation_id', 'unknown')
            
            with electrical_tracer.trace_pe_validation(
                validation_type=validation_type,
                pe_license=pe_license,
                calculation_id=calculation_id
            ) as span:
                try:
                    result = func(*args, **kwargs)
                    span.set_attribute("validation.result", result.validation_result)
                    return result
                except Exception as e:
                    electrical_tracer.add_error_attributes(span, e)
                    raise
        return wrapper
    return decorator

def trace_database_operation(operation: str, table: str):
    """Decorator to trace database operations"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            with electrical_tracer.trace_database_operation(
                operation=operation,
                table=table
            ) as span:
                try:
                    result = func(*args, **kwargs)
                    if hasattr(result, '__len__'):
                        span.set_attribute("db.rows_affected", len(result))
                    return result
                except Exception as e:
                    electrical_tracer.add_error_attributes(span, e)
                    raise
        return wrapper
    return decorator
```

---

## 5. Alerting and Incident Management

### 5.1 Prometheus Alert Rules

#### 5.1.1 Application Alert Rules
```yaml
# infrastructure/monitoring/alert-rules.yaml
groups:
  - name: ultimate-electrical-designer-application
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{job="backend",status_code=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
          service: backend
          team: engineering
        annotations:
          summary: "High error rate detected"
          description: "Backend service error rate is {{ $value | humanizePercentage }} over the last 5 minutes"
          runbook_url: "https://docs.electrical-designer.com/runbooks/high-error-rate"
      
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="backend"}[5m])) > 1.0
        for: 5m
        labels:
          severity: warning
          service: backend
          team: engineering
        annotations:
          summary: "High response time detected"
          description: "Backend service 95th percentile response time is {{ $value }}s"
          runbook_url: "https://docs.electrical-designer.com/runbooks/high-response-time"
      
      - alert: CalculationAccuracyDrop
        expr: calculation_accuracy_percentage < 99.99
        for: 1m
        labels:
          severity: critical
          service: calculation-engine
          team: engineering
        annotations:
          summary: "Calculation accuracy dropped below threshold"
          description: "Calculation accuracy is {{ $value }}% for {{ $labels.calculation_type }}"
          runbook_url: "https://docs.electrical-designer.com/runbooks/calculation-accuracy"
      
      - alert: PEValidationFailure
        expr: rate(pe_validations_total{status="failed"}[5m]) > 0.1
        for: 2m
        labels:
          severity: high
          service: professional-validation
          team: compliance
        annotations:
          summary: "PE validation failures detected"
          description: "PE validation failure rate is {{ $value | humanizePercentage }}"
          runbook_url: "https://docs.electrical-designer.com/runbooks/pe-validation-failure"
      
      - alert: DatabaseConnectionsHigh
        expr: database_connections{status="active"} > 80
        for: 5m
        labels:
          severity: warning
          service: database
          team: engineering
        annotations:
          summary: "High database connection usage"
          description: "Database connection pool usage is {{ $value }} connections"
          runbook_url: "https://docs.electrical-designer.com/runbooks/database-connections"
      
      - alert: CacheHitRatioLow
        expr: cache_hit_ratio < 0.8
        for: 10m
        labels:
          severity: warning
          service: cache
          team: engineering
        annotations:
          summary: "Low cache hit ratio"
          description: "Cache hit ratio is {{ $value | humanizePercentage }} for {{ $labels.cache_type }}"
          runbook_url: "https://docs.electrical-designer.com/runbooks/cache-performance"

  - name: ultimate-electrical-designer-infrastructure
    rules:
      - alert: KubernetesNodeNotReady
        expr: kube_node_status_condition{condition="Ready",status="true"} == 0
        for: 5m
        labels:
          severity: critical
          service: kubernetes
          team: platform
        annotations:
          summary: "Kubernetes node not ready"
          description: "Node {{ $labels.node }} is not ready"
          runbook_url: "https://docs.electrical-designer.com/runbooks/node-not-ready"
      
      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total[5m]) > 0
        for: 5m
        labels:
          severity: warning
          service: kubernetes
          team: platform
        annotations:
          summary: "Pod is crash looping"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is crash looping"
          runbook_url: "https://docs.electrical-designer.com/runbooks/pod-crash-looping"
      
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 10m
        labels:
          severity: warning
          service: infrastructure
          team: platform
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"
          runbook_url: "https://docs.electrical-designer.com/runbooks/high-memory-usage"
      
      - alert: DiskSpaceLow
        expr: (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) / node_filesystem_size_bytes{fstype!="tmpfs"} > 0.8
        for: 10m
        labels:
          severity: warning
          service: infrastructure
          team: platform
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"
          runbook_url: "https://docs.electrical-designer.com/runbooks/disk-space-low"

  - name: ultimate-electrical-designer-compliance
    rules:
      - alert: ComplianceViolation
        expr: increase(professional_validations_total{status="non_compliant"}[1h]) > 0
        for: 1m
        labels:
          severity: critical
          service: compliance
          team: compliance
        annotations:
          summary: "Compliance violation detected"
          description: "{{ $value }} compliance violations detected for {{ $labels.standards }}"
          runbook_url: "https://docs.electrical-designer.com/runbooks/compliance-violation"
      
      - alert: StandardsUpdateRequired
        expr: time() - standards_last_updated_timestamp > 86400
        for: 1m
        labels:
          severity: warning
          service: compliance
          team: compliance
        annotations:
          summary: "Standards update required"
          description: "Standards have not been updated in {{ $value | humanizeDuration }}"
          runbook_url: "https://docs.electrical-designer.com/runbooks/standards-update"
```

### 5.2 Alertmanager Configuration

#### 5.2.1 Alertmanager Setup
```yaml
# infrastructure/monitoring/alertmanager.yaml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  slack_api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
      continue: true
    - match:
        severity: warning
      receiver: 'warning-alerts'
    - match:
        team: compliance
      receiver: 'compliance-alerts'
      continue: true
    - match:
        service: calculation-engine
      receiver: 'calculation-alerts'
      continue: true

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://webhook-receiver:8080/alerts'
        send_resolved: true

  - name: 'critical-alerts'
    slack_configs:
      - channel: '#alerts-critical'
        title: 'Critical Alert - Ultimate Electrical Designer'
        text: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
        send_resolved: true
    email_configs:
      - to: '<EMAIL>'
        subject: 'Critical Alert - {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
    pagerduty_configs:
      - service_key: 'YOUR_PAGERDUTY_SERVICE_KEY'
        description: '{{ .GroupLabels.alertname }} - {{ .CommonAnnotations.summary }}'

  - name: 'warning-alerts'
    slack_configs:
      - channel: '#alerts-warning'
        title: 'Warning Alert - Ultimate Electrical Designer'
        text: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}
        send_resolved: true

  - name: 'compliance-alerts'
    slack_configs:
      - channel: '#compliance-alerts'
        title: 'Compliance Alert - Ultimate Electrical Designer'
        text: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
        send_resolved: true
    email_configs:
      - to: '<EMAIL>'
        subject: 'Compliance Alert - {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Standards: {{ .Labels.standards }}
          {{ end }}

  - name: 'calculation-alerts'
    slack_configs:
      - channel: '#calculation-alerts'
        title: 'Calculation Alert - Ultimate Electrical Designer'
        text: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Calculation Type: {{ .Labels.calculation_type }}
          {{ end }}
        send_resolved: true
    email_configs:
      - to: '<EMAIL>'
        subject: 'Calculation Alert - {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Calculation Type: {{ .Labels.calculation_type }}
          Professional Review Required: Yes
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']
```

---

## 6. Dashboards and Visualization

### 6.1 Grafana Dashboard Configuration

#### 6.1.1 Main Application Dashboard
```json
{
  "dashboard": {
    "id": null,
    "title": "Ultimate Electrical Designer - Overview",
    "tags": ["ultimate-electrical-designer", "overview"],
    "timezone": "browser",
    "refresh": "5s",
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "panels": [
      {
        "id": 1,
        "title": "Request Rate",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
        "targets": [
          {
            "expr": "rate(http_requests_total{job=\"backend\"}[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ],
        "yAxes": [
          {
            "label": "requests/sec",
            "min": 0
          }
        ]
      },
      {
        "id": 2,
        "title": "Response Time Percentiles",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"backend\"}[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"backend\"}[5m]))",
            "legendFormat": "50th percentile"
          }
        ],
        "yAxes": [
          {
            "label": "seconds",
            "min": 0
          }
        ]
      },
      {
        "id": 3,
        "title": "Electrical Calculations",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8},
        "targets": [
          {
            "expr": "rate(electrical_calculations_total[5m])",
            "legendFormat": "{{calculation_type}} - {{status}}"
          }
        ],
        "yAxes": [
          {
            "label": "calculations/sec",
            "min": 0
          }
        ]
      },
      {
        "id": 4,
        "title": "Calculation Accuracy",
        "type": "singlestat",
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8},
        "targets": [
          {
            "expr": "avg(calculation_accuracy_percentage)",
            "legendFormat": "Average Accuracy"
          }
        ],
        "valueName": "current",
        "format": "percent",
        "thresholds": "99.99,99.95",
        "colorBackground": true
      },
      {
        "id": 5,
        "title": "PE Validations",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16},
        "targets": [
          {
            "expr": "rate(pe_validations_total[5m])",
            "legendFormat": "{{validation_type}} - {{status}}"
          }
        ],
        "yAxes": [
          {
            "label": "validations/sec",
            "min": 0
          }
        ]
      },
      {
        "id": 6,
        "title": "Database Connections",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16},
        "targets": [
          {
            "expr": "database_connections",
            "legendFormat": "{{connection_pool}} - {{status}}"
          }
        ],
        "yAxes": [
          {
            "label": "connections",
            "min": 0
          }
        ]
      },
      {
        "id": 7,
        "title": "Cache Performance",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24},
        "targets": [
          {
            "expr": "cache_hit_ratio",
            "legendFormat": "{{cache_type}} hit ratio"
          }
        ],
        "yAxes": [
          {
            "label": "ratio",
            "min": 0,
            "max": 1
          }
        ]
      },
      {
        "id": 8,
        "title": "Error Rate",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24},
        "targets": [
          {
            "expr": "rate(application_errors_total[5m])",
            "legendFormat": "{{error_type}} - {{severity}}"
          }
        ],
        "yAxes": [
          {
            "label": "errors/sec",
            "min": 0
          }
        ]
      }
    ]
  }
}
```

#### 6.1.2 Professional Engineering Dashboard
```json
{
  "dashboard": {
    "id": null,
    "title": "Professional Engineering Metrics",
    "tags": ["professional-engineering", "compliance"],
    "timezone": "browser",
    "refresh": "30s",
    "time": {
      "from": "now-24h",
      "to": "now"
    },
    "panels": [
      {
        "id": 1,
        "title": "Calculation Accuracy by Type",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
        "targets": [
          {
            "expr": "calculation_accuracy_percentage",
            "legendFormat": "{{calculation_type}} - {{standards_applied}}"
          }
        ],
        "yAxes": [
          {
            "label": "accuracy %",
            "min": 99.0,
            "max": 100.0
          }
        ],
        "thresholds": [
          {
            "value": 99.99,
            "colorMode": "critical",
            "op": "lt"
          }
        ]
      },
      {
        "id": 2,
        "title": "PE Validation Status",
        "type": "piechart",
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
        "targets": [
          {
            "expr": "sum by (status) (pe_validations_total)",
            "legendFormat": "{{status}}"
          }
        ]
      },
      {
        "id": 3,
        "title": "Standards Compliance",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8},
        "targets": [
          {
            "expr": "rate(professional_validations_total{status=\"compliant\"}[1h])",
            "legendFormat": "{{standards}} - Compliant"
          },
          {
            "expr": "rate(professional_validations_total{status=\"non_compliant\"}[1h])",
            "legendFormat": "{{standards}} - Non-Compliant"
          }
        ],
        "yAxes": [
          {
            "label": "validations/hour",
            "min": 0
          }
        ]
      },
      {
        "id": 4,
        "title": "Calculation Performance",
        "type": "heatmap",
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8},
        "targets": [
          {
            "expr": "electrical_calculation_duration_seconds_bucket",
            "legendFormat": "{{calculation_type}}"
          }
        ],
        "yAxis": {
          "min": 0,
          "max": 5,
          "unit": "s"
        }
      },
      {
        "id": 5,
        "title": "PE License Validation by State",
        "type": "table",
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16},
        "targets": [
          {
            "expr": "sum by (state) (pe_validations_total)",
            "legendFormat": "{{state}}"
          }
        ],
        "columns": [
          {
            "text": "State",
            "value": "state"
          },
          {
            "text": "Validations",
            "value": "Value"
          }
        ]
      }
    ]
  }
}
```

### 6.2 Compliance Dashboard

#### 6.2.1 Regulatory Compliance Dashboard
```json
{
  "dashboard": {
    "id": null,
    "title": "Regulatory Compliance Dashboard",
    "tags": ["compliance", "regulatory"],
    "timezone": "browser",
    "refresh": "1m",
    "time": {
      "from": "now-7d",
      "to": "now"
    },
    "panels": [
      {
        "id": 1,
        "title": "Compliance Status Overview",
        "type": "stat",
        "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0},
        "targets": [
          {
            "expr": "sum(professional_validations_total{status=\"compliant\"}) / sum(professional_validations_total) * 100",
            "legendFormat": "Compliance Rate"
          }
        ],
        "thresholds": {
          "steps": [
            {
              "color": "red",
              "value": 0
            },
            {
              "color": "yellow",
              "value": 95
            },
            {
              "color": "green",
              "value": 99
            }
          ]
        },
        "unit": "percent"
      },
      {
        "id": 2,
        "title": "Standards Compliance by Type",
        "type": "bargauge",
        "gridPos": {"h": 6, "w": 18, "x": 6, "y": 0},
        "targets": [
          {
            "expr": "sum by (standards) (professional_validations_total{status=\"compliant\"}) / sum by (standards) (professional_validations_total) * 100",
            "legendFormat": "{{standards}}"
          }
        ],
        "thresholds": {
          "steps": [
            {
              "color": "red",
              "value": 0
            },
            {
              "color": "yellow",
              "value": 95
            },
            {
              "color": "green",
              "value": 99
            }
          ]
        },
        "unit": "percent"
      },
      {
        "id": 3,
        "title": "Audit Trail Activity",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6},
        "targets": [
          {
            "expr": "rate(audit_trail_events_total[1h])",
            "legendFormat": "{{event_type}}"
          }
        ],
        "yAxes": [
          {
            "label": "events/hour",
            "min": 0
          }
        ]
      },
      {
        "id": 4,
        "title": "Compliance Violations",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6},
        "targets": [
          {
            "expr": "increase(professional_validations_total{status=\"non_compliant\"}[1h])",
            "legendFormat": "{{standards}} - {{validation_type}}"
          }
        ],
        "yAxes": [
          {
            "label": "violations/hour",
            "min": 0
          }
        ]
      }
    ]
  }
}
```

---


This comprehensive monitoring and observability document establishes a complete framework for monitoring the Ultimate Electrical Designer application. The monitoring system ensures proactive issue detection, performance optimization, and operational excellence.

The observability framework provides:
- **Complete Visibility**: Metrics, logs, traces, and alerts
- **Professional Monitoring**: Electrical engineering calculation monitoring
- **Compliance Tracking**: Regulatory and professional compliance monitoring
- **Performance Optimization**: Real-time performance monitoring and alerting
- **Incident Response**: Automated alerting and escalation procedures

All monitoring systems maintain professional engineering standards and provide complete audit trails for regulatory compliance.

---

**Document Control**
- **Document Owner**: Site Reliability Engineer
- **Review Authority**: Technical Lead, Operations Manager
- **Approval Authority**: Technical Lead, SRE Manager
- **Review Frequency**: Monthly
- **Next Review Date**: [Month + 1]

**Version History**
- **v1.0** (July 2025): Initial monitoring and observability documentation
- **[Future versions will be tracked here]**